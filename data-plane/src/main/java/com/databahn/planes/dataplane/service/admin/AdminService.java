package com.databahn.planes.dataplane.service.admin;

import com.databahn.planes.constants.ChangeFlagAction;
import com.databahn.planes.model.changeflag.AdminChangeFlag;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@AllArgsConstructor
public class AdminService {

  private final AdminHandlerFactory adminHandlerFactory;
  private final ObjectMapper objectMapper;

  public boolean handleAdminChangeFlag(ChangeFlag cf) {
    Map<String, Object> body = cf.body();
    Object entity = body.get("entity");
    AdminChangeFlag adminChangeFlag = objectMapper.convertValue(entity, AdminChangeFlag.class);
    AdminChangeFlag.AdminAction action = adminChangeFlag.getAction();
    AdminChangeFlag.AdminSubAction subAction = adminChangeFlag.getSubAction();
    return adminHandlerFactory
        .get(action, subAction)
        .handleAdminRequest(ChangeFlagAction.parse(cf.action()), adminChangeFlag);
  }
}
