package com.databahn.planes.dataplane.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KafkaConnectorInfo {
  private String name;
  private Connector connector;
  private List<Task> tasks;
  private String type;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class Connector {
    private String state;
    private String worker_id;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class Task {
    private int id;
    private String state;
    private String worker_id;
  }
}
