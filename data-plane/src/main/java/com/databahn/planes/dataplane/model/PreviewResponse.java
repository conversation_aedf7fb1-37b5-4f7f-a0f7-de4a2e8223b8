package com.databahn.planes.dataplane.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PreviewResponse {

  @JsonProperty("log_line")
  String logLine;

  long timestamp;

  String tags;

  @JsonProperty("rule_id")
  String ruleId;

  @JsonProperty("db_event_source_id")
  String sourceId;

  @JsonProperty("db_device_type")
  String deviceType;

  @JsonProperty("db_device_vendor")
  String deviceVendor;

  @JsonProperty("sensitive_type")
  String sensitiveType;

  @JsonProperty("pattern_id")
  String patternId;
}
