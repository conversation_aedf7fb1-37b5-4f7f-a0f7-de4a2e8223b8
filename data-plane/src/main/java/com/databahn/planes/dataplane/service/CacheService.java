package com.databahn.planes.dataplane.service;

import com.databahn.planes.dataplane.model.CacheFunctionality;
import com.google.common.collect.Maps;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class CacheService {

  private final RedisTemplate<String, String> redisTemplate;

  public CacheService(RedisTemplate<String, String> redisTemplate) {
    this.redisTemplate = redisTemplate;
  }

  public void set(CacheFunctionality functionality, String key, String value, Long ttlMillis) {
    String redisKey = functionality.key(key);
    if (ttlMillis != null && ttlMillis > 0) {
      redisTemplate.opsForValue().set(redisKey, value, ttlMillis, TimeUnit.MILLISECONDS);
    } else {
      redisTemplate.opsForValue().set(redisKey, value);
    }
  }

  public void multiSet(CacheFunctionality functionality, Map<String, String> values) {
    Map<String, String> map = Maps.newHashMap();
    values.forEach((key, value) -> map.put(functionality.key(key), value));
    redisTemplate.opsForValue().multiSet(map);
  }

  public String get(CacheFunctionality functionality, String key) {
    String redisKey = functionality.key(key);
    return redisTemplate.opsForValue().get(redisKey);
  }

  public void delete(CacheFunctionality functionality, String key) {
    String redisKey = functionality.key(key);
    redisTemplate.delete(redisKey);
  }

  public void addInSet(CacheFunctionality functionality, String setKey, Set<String> values) {
    String redisKey = functionality.key(setKey);
    redisTemplate.opsForSet().add(redisKey, values.toArray(new String[0]));
  }

  public void removeFromSet(CacheFunctionality functionality, String setKey, String value) {
    String redisKey = functionality.key(setKey);
    redisTemplate.opsForSet().remove(redisKey, value);
  }

  public void removeSet(CacheFunctionality functionality, String setKey) {
    String redisKey = functionality.key(setKey);
    redisTemplate.delete(redisKey);
  }

  public Boolean existsInSet(CacheFunctionality functionality, String setKey, String value) {
    String redisKey = functionality.key(setKey);
    return redisTemplate.opsForSet().isMember(redisKey, value);
  }
}
