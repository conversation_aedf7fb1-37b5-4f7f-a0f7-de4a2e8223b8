package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.lookup.LookupValue;
import com.databahn.planes.model.lookup.LookupValues;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LookupValuesConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  public LookupValuesConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_lookup_values_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.lookup.values"},
      properties = {
        "fetch.min.bytes:${CONSUMER_STATISTICS_FETCH_MIN_BYTES:1048576}",
        "fetch.max.wait.ms:${CONSUMER_STATISTICS_FETCH_MAX_WAIT_MS:500}"
      },
      concurrency = "${CONSUMER_LOOKUP_VALUES_THREADS:2}")
  public void onLookupValues(List<ConsumerRecord<String, String>> records) {
    log.debug("data_plane_lookup_values consumer received {} statistics", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    final List<LookupValue> values = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      Iterable<Header> tenantIdHeader = record.headers().headers("db_tenant_id");
      String tenantId = null;
      if (tenantIdHeader.iterator().hasNext()) {
        tenantId = new String(tenantIdHeader.iterator().next().value());
      }
      try {
        LookupValue lookupValue = objectMapper.readValue(value, LookupValue.class);
        lookupValue.setTenantId(tenantId);
        values.add(lookupValue);
      } catch (JsonProcessingException e) {
        log.error("failed to parse lookup value:" + value, e);
        throw new BatchListenerFailedException("failed to parse lookup value", e, batchFirstRecord);
      }
    }
    sendDataToControlPlane(values, batchFirstRecord);
  }

  private void sendDataToControlPlane(
      List<LookupValue> values, ConsumerRecord<String, String> batchFirstRecord) {
    LookupValues valuesCollected = new LookupValues(values);
    try {
      long start = System.currentTimeMillis();
      Response<Object> response = planeClient.saveLookupValues(valuesCollected);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error("failed to send lookup values to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error("send lookup values to control plane partially failed: {}", response.getData());
      } else {
        log.debug(
            "{} lookup values saved, took {} ms",
            values.size(),
            System.currentTimeMillis() - start);
      }
    } catch (Exception e) {
      log.error("failed to send lookup values to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send lookup values to control plane", e, batchFirstRecord);
    }
  }
}
