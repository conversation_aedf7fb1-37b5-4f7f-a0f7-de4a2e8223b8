package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.config.ControlPlaneClientConfig;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.model.alerts.Alerts;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.databahn.planes.model.checkpoint.CheckpointsRequest;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpoint;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpointRequest;
import com.databahn.planes.model.dataplane.DataPlane;
import com.databahn.planes.model.dataplane.Tenant;
import com.databahn.planes.model.insights.StagingInsights;
import com.databahn.planes.model.lookup.*;
import com.databahn.planes.model.replay.ReplayStatus;
import com.databahn.planes.model.stats.Stats;
import com.databahn.planes.model.stats.VectorStats;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.databind.JsonNode;
import java.net.URI;
import java.util.List;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@FeignClient(
    value = "controlplane-client",
    url = "${controller.baseurl}",
    configuration = ControlPlaneClientConfig.class)
public interface ControlPlaneClient {
  @RequestMapping(method = RequestMethod.POST, value = "/v1/alerts")
  Response<Object> upsertAlerts(@RequestBody Alerts alerts);

  @RequestMapping(method = RequestMethod.GET, value = "/v1/change_flags")
  Response<List<ChangeFlag>> getChangeFlags(
      @RequestHeader("tenantId") String tenantId,
      @RequestHeader("dataPlaneId") String dataPlaneId,
      @RequestParam(name = "since", required = false) Long since,
      @RequestParam("page") Integer page,
      @RequestParam("size") Integer size);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/stats")
  Response<Object> saveStatistics(@RequestBody Stats stats);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/lookup/values")
  Response<Object> saveLookupValues(@RequestBody LookupValues values);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/stats/open_telemetry")
  Response<Object> saveOpenTelemetryStatistics(@RequestBody List<JsonNode> stats);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/stats/vector")
  Response<Object> saveVectorStatistics(@RequestBody VectorStats stats);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/ack")
  Response<Object> saveAcknowledgements(@RequestBody List<Ack> acks);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/insights")
  Response<Object> indexInsights(@RequestBody StagingInsights insights);

  @RequestMapping(method = RequestMethod.POST, value = "/v2/replay-jobs/status")
  Response<Object> sendUpdates(URI baseUrl, @RequestBody List<ReplayStatus> status)
      throws Exception;

  @RequestMapping(method = RequestMethod.GET, value = "/v1/lookup/requests/cache/pending")
  Response<List<LookupCacheRequest>> getPendingLookupCacheRequests(
      @RequestHeader("tenantId") String tenantId, @RequestHeader("dataPlaneId") String dataPlaneId);

  @RequestMapping(method = RequestMethod.POST, value = "/v1/lookup/requests/cache/operations")
  Response<CacheOperationDetails> getLookupCache(@RequestBody CacheOperationRequest request);

  @RequestMapping(method = RequestMethod.PUT, value = "/v1/lookup/requests/cache/status")
  Response<String> updateLookupCacheRequest(@RequestBody CacheOperationStatus status);

  @RequestMapping(method = RequestMethod.PUT, value = "/v1/data_plane/health")
  Response<String> health(@RequestBody DataPlane dataPlane);

  @GetMapping("/v1/data_plane/tenants/{id}")
  Response<List<Tenant>> getTenants(@PathVariable("id") UUID id);

  @GetMapping("/v1/lookup/requests/dynamic/cache/pending")
  Response<List<DynamicLookupCacheRequest>> getPendingDynamicLookupCacheRequests(
      @RequestHeader("tenantId") UUID tenantId, @RequestHeader("dataPlaneId") UUID dataPlaneId);

  @PostMapping("/v1/lookup/requests/dynamic/cache/operations")
  Response<DynamicCacheOperationDetails> getDynamicLookupCache(
      @RequestBody DynamicCacheOperationRequest request);

  @GetMapping("/v1/cloud-source-checkpoint")
  Response<CloudSourceCheckpoint> getCloudSourceCheckpoint(
      @RequestParam("tenantId") UUID tenantId, @RequestParam("sourceId") UUID sourceId);

  @PostMapping("/v1/cloud-source-checkpoint")
  Response<?> saveCloudSourceCheckpoint(@RequestBody CloudSourceCheckpointRequest checkpoint);

  @PostMapping("/v1/cloud-source-checkpoint/find")
  Response<List<CloudSourceCheckpoint>> getCloudSourceCheckpoints(
      @RequestBody CheckpointsRequest request);

  @RequestMapping(
      method = RequestMethod.POST,
      value = "/v1/metrics/{type}",
      consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  Response<Object> saveMetrics(@PathVariable("type") String metricType, @RequestBody String stats);
}
