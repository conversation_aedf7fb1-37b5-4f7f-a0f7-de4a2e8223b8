package com.databahn.planes.dataplane.constants;

public class CdcConstants extends KafkaConnectConstants {

  public static final String VENDOR_SAVIYANTS = "saviynt";
  public static final String CONNECTOR_CLASS = "connector.class";
  public static final String TOPIC_PREFIX = "topic.prefix";
  public static final String DATABASE_HOSTNAME = "database.hostname";
  public static final String DATABASE_PORT = "database.port";
  public static final String DATABASE_USER = "database.user";
  public static final String DATABASE_PASSWORD = "database.password";
  public static final String DATABASE_DBNAME = "database.dbname";
  public static final String DATABASE_INCLUDE_LIST = "database.include.list";
  public static final String TABLE_INCLUDE_LIST = "table.include.list";
  public static final String SNAPSHOT_NEW_TABLE = "snapshot.new.table";
  public static final String MAX_BATCH_SIZE = "max.batch.size";
  public static final String MAX_QUEUE_SIZE = "max.queue.size";
  public static final String DATABASE_SERVER_NAME = "database.server.name";
  public static final String DATABASE_SERVER_ID = "database.server.id";
  public static final String INCLUDE_SCHEMA_CHANGES = "include.schema.changes";
  public static final String SCHEMA_HISTORY_INTERNAL_KAFKA_BOOTSTRAP_SERVERS =
      "schema.history.internal.kafka.bootstrap.servers";
  public static final String SCHEMA_HISTORY_INTERNAL_STORE_ONLY_CAPTURED_DATABASES_DDL =
      "schema.history.internal.store.only.captured.databases.ddl";
  public static final String SCHEMA_HISTORY_INTERNAL_KAFKA_TOPIC =
      "schema.history.internal.kafka.topic";
  public static final String TIME_PRECISION_MODE = "time.precision.mode";

  public static final String TRANSFORMS = "transforms";
  public static final String TRANSFORMS_VALUE_REROUTE = "Reroute";
  public static final String TRANSFORMS_REROUTE_TYPE = "transforms.Reroute.type";

  public static final String TRANSFORMS_REROUTE_TYPE_VALUE =
      "org.apache.kafka.connect.transforms.RegexRouter";
  public static final String TRANSFORMS_REROUTE_REGEX = "transforms.Reroute.regex";
  public static final String TRANSFORMS_REROUTE_REGEX_VALUE = ".*";
  public static final String TRANSFORMS_REROUTE_REPLACEMENT = "transforms.Reroute.replacement";

  public static final String DB_RAW_INPUT_CDC_TOPIC = "db.raw.input.cdc";
}
