package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.model.SecretsManagerResponse;
import java.util.List;
import java.util.Set;
import org.springframework.web.bind.annotation.*;

public interface SecretsManagerClient {
  @RequestMapping(method = RequestMethod.GET, value = "/secrets")
  List<SecretsManagerResponse> getSecrets(@RequestParam("ids") Set<String> secretIds);
}
