package com.databahn.planes.dataplane.service;

import com.databahn.planes.dataplane.client.SecretsManagerClient;
import com.databahn.planes.dataplane.client.SecretsManagerClientFactory;
import com.databahn.planes.dataplane.model.SecretsManagerResponse;
import com.databahn.planes.dataplane.model.SecretsRequest;
import com.databahn.planes.dataplane.model.SecretsResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@AllArgsConstructor
@Slf4j
public class SecretsService {

  private final SensitiveCache cache;
  private final SecretsManagerClientFactory clientFactory;

  public SecretsResponse getSecrets(SecretsRequest request) {
    SecretsResponse response = new SecretsResponse();
    Set<String> secretIds = request.getSecretIds();
    if (secretIds == null || secretIds.isEmpty()) {
      return response;
    }
    log.debug("Fetching secret: {}", request.getTenantId());
    Set<String> secretIdsToFetch = new HashSet<>();
    for (String secretId : secretIds) {
      Optional<Map<String, String>> cachedSecrets = cache.get(secretId);
      if (cachedSecrets.isPresent()) {
        response.getSecrets().put(secretId, cachedSecrets.get());
      } else {
        secretIdsToFetch.add(secretId);
      }
    }
    if (secretIdsToFetch.isEmpty()) {
      return response;
    }

    Optional<SecretsManagerClient> client = clientFactory.getClient(request.getTenantId());
    if (client.isEmpty()) {
      throw new IllegalArgumentException("Client not found for tenantId: " + request.getTenantId());
    }

    List<SecretsManagerResponse> managerSecrets = client.get().getSecrets(secretIdsToFetch);
    Map<String, Map<String, String>> secrets = new HashMap<>();
    Map<String, String> errors = new HashMap<>();

    for (SecretsManagerResponse managerSecret : managerSecrets) {
      String secretId = managerSecret.getId();
      try {
        Map<String, String> value = managerSecret.getValueForJson();
        if (value == null) {
          errors.put(secretId, "Secret value is empty");
        } else {
          secrets.put(secretId, value);
        }
      } catch (Exception e) {
        errors.put(secretId, ExceptionUtils.getRootCauseMessage(e));
      }
    }

    for (String secretId : secretIdsToFetch) {
      if (secrets.containsKey(secretId)) {
        Map<String, String> val = secrets.get(secretId);
        response.getSecrets().put(secretId, val);
        cache.put(secretId, val);
      } else if (errors.containsKey(secretId)) {
        response.getErrors().put(secretId, errors.get(secretId));
      } else {
        response.getErrors().put(secretId, "Secret not found");
      }
    }
    return response;
  }
}
