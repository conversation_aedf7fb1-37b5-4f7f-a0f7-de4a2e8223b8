package com.databahn.planes.dataplane.controllers;

import com.databahn.planes.dataplane.client.PreviewServiceClient;
import com.databahn.planes.dataplane.model.PreviewResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/log-preview")
public class PreviewController {

  private final PreviewServiceClient previewServiceClient;

  @GetMapping("{key}")
  @CrossOrigin(origins = {"https://${urls.databahn_app}", "http://localhost:3000"})
  public List<PreviewResponse> get(@PathVariable String key) {
    return previewServiceClient.getPreview(key);
  }
}
