package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.config.FeignNoProxyConfig;
import com.databahn.planes.dataplane.model.PreviewResponse;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    value = "preview-client",
    configuration = FeignNoProxyConfig.class,
    url = "${urls.preview_service_url:http://preview-service.processing.svc.cluster.local:8080}")
public interface PreviewServiceClient {
  @GetMapping("/preview")
  List<PreviewResponse> getPreview(@RequestParam("key") String key);
}
