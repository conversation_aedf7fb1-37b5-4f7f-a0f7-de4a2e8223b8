package com.databahn.planes.dataplane.model;

import java.net.URI;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.annotation.Configuration;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Configuration
public class ConnectorPayload {
  private String name;

  private String topic;
  private Map<String, Object> config;
  private URI uri;
  private int partitions = 4;
  private int replicationFactor = 3;

  public void buildURI(String uri) {
    try {
      this.uri = URI.create(uri);
    } catch (Exception e) {
      throw new IllegalArgumentException("Invalid URI: " + uri);
    }
  }
}
