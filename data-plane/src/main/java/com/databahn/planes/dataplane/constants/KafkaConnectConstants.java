package com.databahn.planes.dataplane.constants;

public class KafkaConnectConstants {

  public static final String REGEX_STAR = ".*";
  public static final String COLON = ":";

  public static final String DESTINATION_TYPE_SNOWFLAKE = "Snowflake";
  public static final String DESTINATION_TYPE = "destination_type";

  public static final String CONNECTOR_STATUS_RUNNING = "RUNNING";
  public static final String CONNECTOR_STATUS_STOPPED = "STOPPED";
  public static final String ACK_STATUS_SUCCESS = "SUCCESS";
  public static final String ACK_STATUS_FAILURE = "FAILURE";
  public static final String DESTINATION_STATUS = "status";

  // ACK Constants
  public static final String ID = "id";
  public static final String REQUEST_ID = "request_id";
  public static final String CUSTOMER_ID = "customer_id";
  public static final String ENTITY_ID = "entity_id";
  public static final String ENTITY_TYPE = "entity_type";
  public static final String ENTITY_VERSION = "entity_version";
  public static final String SERVICE_NAME = "service_name";
  public static final String TENANT_ID = "tenant_id";
  public static final String ACTION = "action";
  public static final String ERROR = "error";
  public static final String PROGRESS = "progress";

  public static final String VENDOR = "vendor";
}
