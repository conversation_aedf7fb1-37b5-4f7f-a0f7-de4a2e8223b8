package com.databahn.planes.dataplane.data;

import java.time.Duration;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class LockService {

  private RedisTemplate<String, String> redisTemplate;

  public boolean lock(String key, String lockId, Duration ttl) {
    Boolean success = redisTemplate.opsForValue().setIfAbsent(key, lockId, ttl);
    return success != null && success;
  }

  public void unlock(String key, String lockId) {
    String value = redisTemplate.opsForValue().get(key);
    if (lockId.equals(value)) {
      redisTemplate.delete(key);
    }
  }
}
