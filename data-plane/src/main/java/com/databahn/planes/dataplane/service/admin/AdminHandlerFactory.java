package com.databahn.planes.dataplane.service.admin;

import com.databahn.planes.model.changeflag.AdminChangeFlag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class AdminHandlerFactory {

  private KSqlDbRequestHandler kSqlDbRequestHandler;

  public AdminRequestHandler get(
      AdminChangeFlag.AdminAction action, AdminChangeFlag.AdminSubAction subAction) {
    if (action == AdminChangeFlag.AdminAction.SETUP_KSQL_DB) {
      return kSqlDbRequestHandler;
    }
    throw new IllegalArgumentException("No admin handler for " + action + "/" + subAction);
  }
}
