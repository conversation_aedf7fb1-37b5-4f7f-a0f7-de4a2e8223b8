package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.stats.VectorStat;
import com.databahn.planes.model.stats.VectorStats;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class VectorStatisticsConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  public VectorStatisticsConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_vector_statistics_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.metrics.vector"},
      concurrency = "${consumer.statistics.vector.threads:1}")
  public void onStatistics(List<ConsumerRecord<String, String>> records) {
    log.debug("data_plane_vector_statistics_consumer received {} statistics", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    List<VectorStat> stats = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      try {
        VectorStat stat = objectMapper.readValue(value, VectorStat.class);
        stats.add(stat);
      } catch (JsonProcessingException e) {
        log.error("failed to parse vector statistics:" + value, e);
        throw new BatchListenerFailedException(
            "failed to parse vector statistics", e, batchFirstRecord);
      }
    }
    VectorStats statsCollected = new VectorStats(stats);
    try {
      Response<Object> response = planeClient.saveVectorStatistics(statsCollected);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error("failed to send vector statistics to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error(
            "send vector statistics to control plane partially failed: {}", response.getData());
      } else {
        log.debug("{} vector statistics saved", stats.size());
      }
    } catch (Exception e) {
      log.error("failed to send vector statistics to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send vector statistics to control plane", e, batchFirstRecord);
    }
  }
}
