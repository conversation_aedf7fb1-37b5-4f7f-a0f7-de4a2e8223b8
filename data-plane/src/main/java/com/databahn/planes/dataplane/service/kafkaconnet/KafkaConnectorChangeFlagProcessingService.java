package com.databahn.planes.dataplane.service.kafkaconnet;

import com.databahn.planes.constants.ChangeFlagConstants;
import com.databahn.planes.dataplane.constants.CdcConstants;
import com.databahn.planes.dataplane.constants.KafkaConnectConstants;
import com.databahn.planes.dataplane.constants.SnowFlakeConstants;
import com.databahn.planes.dataplane.model.ConnectorPayload;
import com.databahn.planes.model.changeflag.ChangeFlag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@AllArgsConstructor
@Slf4j
@Service
public class KafkaConnectorChangeFlagProcessingService {

  private final KafkaAdmin adminClient;
  private final KafkaConnectorManagementService kafkaConnectorManagementService;
  private final ApplicationContext context;

  @Autowired
  public KafkaConnectorChangeFlagProcessingService(
      ApplicationContext context,
      KafkaConnectorManagementService kafkaConnectorManagementService,
      @Qualifier("inputKafkaAdmin") KafkaAdmin adminClient) {
    this.context = context;
    this.kafkaConnectorManagementService = kafkaConnectorManagementService;
    this.adminClient = adminClient;
  }

  /**
   * This method checks if a new topic needs to be created for kafka connector source connector
   * /dispenser sink based on the change flag. If the kafka connector condition and the action is
   * 'add' or 'update', it creates or updates a topic respectively. If the action is 'delete', it
   * deletes the connector.
   *
   * @param cf The change flag containing the action and entity details.
   * @param action The action to be performed (add, update, delete).
   * @param entity The entity containing the destination type and other configuration details.
   * @return A boolean indicating if the change flag was invalid (true if invalid, false otherwise).
   */
  public boolean processChangeFlagForConnector(
      ChangeFlag cf, String action, Map<String, Object> entity) {

    ConnectorPayload connectorPayload = validateChangeFlagAndGeneratePayloadForConnector(entity);

    if (connectorPayload != null) {
      String entityId = entity.get(ChangeFlagConstants.ID).toString();
      try {
        switch (action) {
          case ChangeFlagConstants.ADD:
            log.info(
                "Creating topic for entity :- {}  Connector: - {}",
                entityId,
                connectorPayload.getName());
            if (connectorPayload.getTopic() != null) {

              NewTopic topic =
                  new NewTopic(
                      connectorPayload.getTopic(),
                      connectorPayload.getPartitions(),
                      (short) connectorPayload.getReplicationFactor());
              adminClient.createOrModifyTopics(topic);
              log.info(
                  "Topic created for entity :- {}  Connector: - {}",
                  entityId,
                  connectorPayload.getName());
            } else {
              log.info(
                  "Topic is null for entity :- {}  Connector: - {}",
                  entityId,
                  connectorPayload.getName());
            }
            kafkaConnectorManagementService.postToConnector(cf, entity, connectorPayload);
            log.info(" topic  created and Connector started for entity - {}", entityId);
            break;
          case ChangeFlagConstants.UPDATE:
            kafkaConnectorManagementService.updateConnector(cf, entity, connectorPayload);
            break;
          case ChangeFlagConstants.DELETE:
            kafkaConnectorManagementService.stopConnector(cf, entity, connectorPayload);
            break;
        }
        log.info(" KafkaConnect change flag {} , Processed", cf);
      } catch (Exception exception) {
        log.error(
            "In KafkaConnect change flag {} , Skipping further execution , Exception :- {} ",
            cf,
            exception.getMessage());
        return true;
      }
    } else {
      log.debug("KafkaConnect is not present change flag {} , Processed", cf);
    }
    return false;
  }

  private ConnectorPayload validateChangeFlagAndGeneratePayloadForConnector(
      Map<String, Object> entity) {

    Object destinationType = entity.get(SnowFlakeConstants.DESTINATION_TYPE);
    Object sourceType = entity.get(KafkaConnectConstants.VENDOR);
    KafkaConnector kafkaConnector = null;
    if (Objects.nonNull(destinationType)) {

      switch (destinationType.toString()) {
        case SnowFlakeConstants.DESTINATION_TYPE_SNOWFLAKE:
          kafkaConnector = (KafkaConnector) context.getBean("snowFlakeConnector");
          return kafkaConnector.getPayload(entity);
      }
    }

    if (Objects.nonNull(sourceType)) {

      switch (sourceType.toString()) {
        case CdcConstants.VENDOR_SAVIYANTS:
          kafkaConnector = (KafkaConnector) context.getBean("cdcConnector");
          return kafkaConnector.getPayload(entity);
      }
    }

    return null;
  }
}
