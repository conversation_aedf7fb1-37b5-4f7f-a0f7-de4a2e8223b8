package com.databahn.planes.dataplane.config;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.CommonErrorHandler;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.FixedBackOff;

@Configuration
@EnableKafka
public class KafkaConfiguration {

  @Value("${kafka.input.bootstrap_brokers}")
  private String inputKafkaBrokers;

  @Value("${kafka.processing.bootstrap_brokers}")
  private String processingKafka;

  @Value("${kafka.batch_size:5000}")
  private int batchSize;

  private static final long RETRY_SECONDS = 2 * 60L;
  private static final long RECOVERY_SECONDS = 60 * 60 * 24L;

  @Bean
  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>>
      inputKafkaListenerContainerFactory() {
    return buildListenerFactory(inputKafkaBrokers);
  }

  @Bean
  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>>
      processingKafkaListenerContainerFactory() {
    return buildListenerFactory(processingKafka);
  }

  private KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>>
      buildListenerFactory(String brokers) {
    ConcurrentKafkaListenerContainerFactory<String, String> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(consumerFactory(brokers));
    factory.getContainerProperties().setPollTimeout(3000);
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
    factory.setCommonErrorHandler(kafkaCommonErrorHandler());
    return factory;
  }

  public ConsumerFactory<String, String> consumerFactory(String brokers) {
    return new DefaultKafkaConsumerFactory<>(consumerConfigs(brokers));
  }

  public Map<String, Object> consumerConfigs(String brokers) {
    Map<String, Object> props = new HashMap<>();
    props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, brokers);
    props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
    props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, batchSize);
    props.put(
        ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
        "org.apache.kafka.clients.consumer.RoundRobinAssignor");
    props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    return props;
  }

  public ProducerFactory<String, String> producerFactory(String brokers) {
    return new DefaultKafkaProducerFactory<>(producerConfigs(brokers));
  }

  public Map<String, Object> producerConfigs(String brokers) {
    Map<String, Object> props = new HashMap<>();
    props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, brokers);
    props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    return props;
  }

  @Bean
  public KafkaTemplate<String, String> inputKafkaTemplate() {
    return new KafkaTemplate<>(producerFactory(inputKafkaBrokers));
  }

  @Bean
  public KafkaTemplate<String, String> processingKafkaTemplate() {
    return new KafkaTemplate<>(producerFactory(processingKafka));
  }

  private CommonErrorHandler kafkaCommonErrorHandler() {
    Logger logger = LoggerFactory.getLogger("KafkaErrorHandler");
    DefaultErrorHandler defaultErrorHandler =
        new DefaultErrorHandler(
            (r, e) -> {
              long offset = r.offset();
              int partition = r.partition();
              String topic = r.topic();
              String message =
                  String.format(
                      "Unprocessed: Failed to handle kafka message %s, offset %d, partition %d, topic %s",
                      offset, partition, topic);
              logger.error(message, e);
            },
            new FixedBackOff(RETRY_SECONDS * 1000L, RECOVERY_SECONDS / RETRY_SECONDS));
    return defaultErrorHandler;
  }

  @Bean(name = "inputKafkaAdmin")
  public KafkaAdmin inputKafkaAdmin() {
    Map<String, Object> configs = new HashMap<>();
    configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, this.inputKafkaBrokers);
    return new KafkaAdmin(configs);
  }

  @Bean(name = "processingKafkaAdmin")
  public KafkaAdmin processingKafkaAdmin() {
    Map<String, Object> configs = new HashMap<>();
    configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, this.processingKafka);
    return new KafkaAdmin(configs);
  }

  @Bean
  public NewTopic topic() {
    return new NewTopic("db.throttler.dlq", 2, (short) 3);
  }

  @Bean
  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, byte[]>>
      processingKafkaByteArrayListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, byte[]> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(byteArrayConsumerFactory(this.processingKafka));
    factory.getContainerProperties().setPollTimeout(3000);
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
    factory.setCommonErrorHandler(kafkaCommonErrorHandler());
    return factory;
  }

  public ConsumerFactory<String, byte[]> byteArrayConsumerFactory(String broker) {
    return new DefaultKafkaConsumerFactory<>(byteArrayConsumerConfigs(broker));
  }

  public Map<String, Object> byteArrayConsumerConfigs(String broker) {
    Map<String, Object> props = new HashMap<>();
    props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, broker);
    props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
    props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, batchSize);
    props.put(
        ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
        "org.apache.kafka.clients.consumer.RoundRobinAssignor");
    props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
    return props;
  }
}
