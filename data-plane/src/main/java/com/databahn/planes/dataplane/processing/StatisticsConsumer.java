package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.stats.Stat;
import com.databahn.planes.model.stats.Stats;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class StatisticsConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  public StatisticsConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_statistics_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.management.statistics"},
      properties = {
        "fetch.min.bytes:${CONSUMER_STATISTICS_FETCH_MIN_BYTES:1048576}",
        "fetch.max.wait.ms:${CONSUMER_STATISTICS_FETCH_MAX_WAIT_MS:500}"
      },
      concurrency = "${CONSUMER_STATISTICS_THREADS:4}")
  public void onStatistics(List<ConsumerRecord<String, String>> records) {
    log.debug("data_plane_statistics_consumer received {} statistics", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    final List<Stat> stats = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      try {
        Stat stat = objectMapper.readValue(value, Stat.class);
        stats.add(stat);
      } catch (JsonProcessingException e) {
        log.error("failed to parse statistics:" + value, e);
        throw new BatchListenerFailedException("failed to parse statistics", e, batchFirstRecord);
      }
    }
    sendDataToControlPlane(stats, batchFirstRecord);
  }

  private void sendDataToControlPlane(
      List<Stat> stats, ConsumerRecord<String, String> batchFirstRecord) {
    Stats statsCollected = new Stats(stats);
    try {
      long start = System.currentTimeMillis();
      Response<Object> response = planeClient.saveStatistics(statsCollected);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error("failed to send statistics to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error("send statistics to control plane partially failed: {}", response.getData());
      } else {
        log.debug(
            "{} statistics saved, took {} ms", stats.size(), System.currentTimeMillis() - start);
      }
    } catch (Exception e) {
      log.error("failed to send statistics to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send statistics to control plane", e, batchFirstRecord);
    }
  }
}
