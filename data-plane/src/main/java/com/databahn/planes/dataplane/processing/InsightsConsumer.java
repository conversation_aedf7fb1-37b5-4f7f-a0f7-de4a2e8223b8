package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.insights.DeviceInsightsAggregation;
import com.databahn.planes.model.insights.StagingInsight;
import com.databahn.planes.model.insights.StagingInsights;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class InsightsConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  private static final String DB_TENANT_ID = "db_tenant_id";
  private static final String LOOKUP_ID = "lookup_id";
  private static final String TYPE = "type";
  private static final String DB_EVENT_SOURCE_ID = "db_event_source_id";
  private static final String DB_DATA_PLANE_ID = "db_data_plane_id";
  private static final String KEY_1 = "key1";
  private static final String KEY_2 = "key2";
  private static final String KEY_3 = "key3";
  private static final String KEY_4 = "key4";
  private static final String KEY_5 = "key5";

  public InsightsConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_device_insights_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.insights.device.staging.out"},
      concurrency = "${consumer.insights.threads:2}")
  public void onInsights(List<ConsumerRecord<String, String>> records) {
    log.debug("data_plane_statistics_consumer received {} insights", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    List<StagingInsight> insights = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      try {
        DeviceInsightsAggregation agg =
            objectMapper.readValue(value, DeviceInsightsAggregation.class);
        String insightsKey = agg.insightsKey();
        List<NameValuePair> pairs = URLEncodedUtils.parse(insightsKey, StandardCharsets.UTF_8);
        String tenantId = null;
        String lookupId = null;
        String sourceId = null;
        String dataPlaneId = null;
        String type = null;
        String key1, key2, key3, key4, key5;
        key1 = key2 = key3 = key4 = key5 = null;
        for (NameValuePair pair : pairs) {
          switch (pair.getName()) {
            case DB_TENANT_ID -> tenantId = pair.getValue();
            case LOOKUP_ID -> lookupId = pair.getValue();
            case DB_EVENT_SOURCE_ID -> sourceId = pair.getValue();
            case DB_DATA_PLANE_ID -> dataPlaneId = pair.getValue();
            case KEY_1 -> key1 = pair.getValue();
            case TYPE -> type = pair.getValue();
            case KEY_2 -> key2 = pair.getValue();
            case KEY_3 -> key3 = pair.getValue();
            case KEY_4 -> key4 = pair.getValue();
            case KEY_5 -> key5 = pair.getValue();
          }
        }
        if (tenantId == null
            || sourceId == null
            || dataPlaneId == null
            || type == null
            || (key1 == null && key2 == null && key3 == null && key4 == null && key5 == null)) {
          log.error("failed to parse insights key: {}", insightsKey);
          throw new RuntimeException("failed to parse insights key " + insightsKey);
        }
        StagingInsight deviceInsight =
            new StagingInsight(
                key1,
                key2,
                key3,
                key4,
                key5,
                type,
                sourceId,
                tenantId,
                dataPlaneId,
                lookupId,
                agg.minTime(),
                agg.maxTime(),
                agg.count());
        insights.add(deviceInsight);
      } catch (Exception e) {
        log.error("failed to parse DeviceInsight:{}. Ignoring.", value, e);
      }
    }
    StagingInsights insightsCollected = new StagingInsights(insights);
    try {
      Response<Object> response = planeClient.indexInsights(insightsCollected);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error("failed to send insights to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error("send insights to control plane partially failed: {}", response.getData());
      } else {
        log.debug("{} insights saved", insights.size());
      }
    } catch (Exception e) {
      log.error("failed to send insights to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send insights to control plane", e, batchFirstRecord);
    }
  }
}
