package com.databahn.planes.dataplane.model;

public enum CacheFunctionality {
  LOOKUP_VOLUME_CONTROLLER("#db.lookup.volume.controller#"),
  LOOKUP_ENRICHMENT("#db.lookup.enrichment#"),
  PULL_CHECKPOINT("#db.pull.checkpoint#");
  private final String prefix;

  private CacheFunctionality(String prefix) {
    this.prefix = prefix;
  }

  public String prefix() {
    return prefix;
  }

  public String key(String key) {
    return prefix + key;
  }
}
