package com.databahn.planes.dataplane.constants;

public class SnowFlakeConstants extends KafkaConnectConstants {

  public static final String TOPICS_REGEX = "topics.regex";

  public static final String SNOWFLAKE_TOPIC_PREFIX = "db.destination.snowflake.";
  public static final String SNOWFLAKE_CONNECTOR_NAME_PREFIX = "destination_snowflake_";
  public static final String SNOWFLAKE_TOPIC2TABLE_MAP = "snowflake.topic2table.map";
  public static final String SNOWFLAKE_URL_NAME = "snowflake.url.name";
  public static final String SNOWFLAKE_USER_NAME = "snowflake.user.name";
  public static final String SNOWFLAKE_ROLE_NAME = "snowflake.role.name";
  public static final String SNOWFLAKE_PRIVATE_KEY = "snowflake.private.key";
  public static final String SNOWFLAKE_DATABASE_NAME = "snowflake.database.name";
  public static final String SNOWFLAKE_SCHEMA_NAME = "snowflake.schema.name";
  public static final String TASKS_MAX = "tasks.max";
  public static final String CONNECTOR_CLASS = "connector.class";
  public static final String CONNECTOR_CLASS_SNOWFLAKE =
      "com.snowflake.kafka.connector.SnowflakeSinkConnector";
  public static final String SCHEMAS_ENABLE = "schemas.enable";
  public static final String BEHAVIOR_ON_NULL_VALUES = "behavior.on.null.values";
  public static final String KEY_CONVERTER = "key.converter";
  public static final String VALUE_CONVERTER = "value.converter";

  public static final String JSON_CONVERTER_CLASS = "org.apache.kafka.connect.json.JsonConverter";
  public static final String KEY_CONVERTER_SCHEMAS_ENABLE = "key.converter.schemas.enable";
  public static final String VALUE_CONVERTER_SCHEMAS_ENABLE = "value.converter.schemas.enable";
  public static final String SNOWFLAKE_INGESTION_METHOD = "snowflake.ingestion.method";
  public static final String SNOWFLAKE_INGESTION_METHOD_SNOWPIPE_STREAMING = "SNOWPIPE_STREAMING";
  public static final String SNOWFLAKE_ENABLE_SCHEMATIZATION = "snowflake.enable.schematization";
  public static final String ERRORS_TOLERANCE = "errors.tolerance";
  public static final String ERRORS_LOG_ENABLE = "errors.log.enable";
  public static final String ERRORS_LOG_INCLUDE_MESSAGES = "errors.log.include.messages";
  public static final String BUFFER_COUNT_RECORDS = "buffer.count.records";
  public static final String BUFFER_FLUSH_TIME = "buffer.flush.time";
  public static final String BUFFER_SIZE_BYTES = "buffer.size.bytes";
  public static final String DEFAULT = "DEFAULT";

  public static final String ALL = "all";

  // DESTINATION CONFIGS KEYS

  public static final String DESTINATION_CONFIG_SNOWFLAKE_TABLE_NAME = "snowflake_table_name";
  public static final String DESTINATION_CONFIG_SNOWFLAKE_URL_NAME = "snowflake_url_name";
  public static final String DESTINATION_CONFIG_SNOWFLAKE_USER_NAME = "snowflake_user_name";
  public static final String DESTINATION_CONFIG_SNOWFLAKE_ROLE_NAME = "snowflake_role_name";
  public static final String DESTINATION_CONFIG_SNOWFLAKE_PRIVATE_KEY = "snowflake_private_key";
  public static final String DESTINATION_CONFIG_SNOWFLAKE_DATABASE_NAME = "snowflake_database_name";
  public static final String DESTINATION_CONFIG_SNOWFLAKE_SCHEMA_NAME = "snowflake_schema_name";

  public static final String REGEX_STAR = ".*";
  public static final String COLON = ":";

  public static final String DESTINATION_TYPE_SNOWFLAKE = "Snowflake";
  public static final String DESTINATION_TYPE = "destination_type";

  public static final String CONNECTOR_STATUS_RUNNING = "RUNNING";
  public static final String CONNECTOR_STATUS_STOPPED = "STOPPED";
}
