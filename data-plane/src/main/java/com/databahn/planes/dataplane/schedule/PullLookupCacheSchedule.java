package com.databahn.planes.dataplane.schedule;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.dataplane.config.DataPlaneConfig;
import com.databahn.planes.dataplane.data.LockService;
import com.databahn.planes.dataplane.data.PullCheckpointService;
import com.databahn.planes.dataplane.model.CacheFunctionality;
import com.databahn.planes.dataplane.service.CacheService;
import com.databahn.planes.model.constants.LookupType;
import com.databahn.planes.model.dataplane.TenantDataPlane;
import com.databahn.planes.model.lookup.*;
import com.databahn.planes.response.Response;
import java.time.Duration;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PullLookupCacheSchedule {

  private static final String LOCK_PULL_LOOKUP_CACHE_FLAGS = "pull_lookup_cache";
  private static final String LOCK_PULL_DYNAMIC_LOOKUP_CACHE_FLAGS = "pull_dynamic_lookup_cache";
  private DataPlaneConfig dataPlaneConfig;
  private ControlPlaneClient controlPlaneClient;
  private CacheService cacheService;
  private LockService lockService;

  private PullCheckpointService pullCheckpointService;

  public PullLookupCacheSchedule(
      DataPlaneConfig dataPlaneConfig,
      ControlPlaneClient controlPlaneClient,
      CacheService cacheService,
      LockService lockService,
      PullCheckpointService pullCheckpointService) {
    this.dataPlaneConfig = dataPlaneConfig;
    this.controlPlaneClient = controlPlaneClient;
    this.cacheService = cacheService;
    this.lockService = lockService;
    this.pullCheckpointService = pullCheckpointService;
    log.info("PullLookupCacheSchedule initialized");
  }

  @Scheduled(cron = "5 */2 * * * *")
  public void pullDynamicLookupCache() {
    String lockId = UUID.randomUUID().toString();
    boolean locked =
        lockService.lock(LOCK_PULL_DYNAMIC_LOOKUP_CACHE_FLAGS, lockId, Duration.ofMinutes(6));
    if (!locked) {
      log.info("Failed to acquire lock for pulling dynamic lookup cache");
      return;
    }
    try {
      List<TenantDataPlane> tenantDataPlanes = dataPlaneConfig.getTenantDataPlanes();
      for (TenantDataPlane tenantDataPlane : tenantDataPlanes) {
        Response<List<DynamicLookupCacheRequest>> requests =
            controlPlaneClient.getPendingDynamicLookupCacheRequests(
                tenantDataPlane.getTenantId(), tenantDataPlane.getDataPlaneId());
        if (requests == null || requests.getData() == null || requests.getData().isEmpty()) {
          log.info(
              "No pending dynamic lookup cache requests for tenant {}, dpId: {}",
              tenantDataPlane.getTenantId(),
              tenantDataPlane.getDataPlaneId());
          continue;
        }
        List<DynamicLookupCacheRequest> cacheRequests = requests.getData();
        for (DynamicLookupCacheRequest cacheRequest : cacheRequests) {
          log.info(
              "Processing dynamic lookup cache request lookupId:{}, type:{}, enrichmentId:{}",
              cacheRequest.getLookupId(),
              cacheRequest.getLookupType(),
              cacheRequest.getEnrichmentId());
          Long lastTimestamp = null;
          try {
            String enrichmentIdStr =
                cacheRequest.getEnrichmentId() == null
                    ? null
                    : cacheRequest.getEnrichmentId().toString();
            Long checkPoint =
                pullCheckpointService.getDynamicLookupPullCheckPoint(
                    tenantDataPlane.getTenantId().toString(),
                    tenantDataPlane.getDataPlaneId().toString(),
                    cacheRequest.getLookupType(),
                    cacheRequest.getLookupId().toString(),
                    enrichmentIdStr);
            Object[] searchAfter = null;
            int count = 0;
            while (true) {
              DynamicCacheOperationRequest request = new DynamicCacheOperationRequest();
              request.setRequestId(cacheRequest.getId());
              request.setLookupId(cacheRequest.getLookupId());
              request.setEnrichmentId(cacheRequest.getEnrichmentId());
              request.setSize(1000);
              request.setSearchAfter(searchAfter);
              request.setAfterTimestamp(checkPoint);
              Response<DynamicCacheOperationDetails> operationDetails =
                  controlPlaneClient.getDynamicLookupCache(request);
              if (operationDetails == null
                  || operationDetails.getData() == null
                  || operationDetails.getData().getOperation() == null
                  || operationDetails.getData().getOperation().isEmpty()) {
                log.info(
                    "No more lookup cache operation for lookupId:{}, type:{}, enrichmentId:{}",
                    cacheRequest.getLookupId(),
                    cacheRequest.getLookupType(),
                    cacheRequest.getEnrichmentId());
                break;
              }
              count += operationDetails.getData().getOperation().count();
              CacheOperation operation = operationDetails.getData().getOperation();
              performCacheOperation(cacheRequest.getLookupType(), operation);
              searchAfter = operationDetails.getData().getSearchAfter();
              lastTimestamp = operationDetails.getData().getCheckpointTimestamp();
              if (searchAfter == null || searchAfter.length == 0) {
                break;
              }
            }
            if (lastTimestamp != null) {
              log.info(
                  "Dynamic lookup cache request lookupId:{}, type:{}, enrichmentId:{}, count: {}, newCheckpoint: {} processed successfully for {}",
                  cacheRequest.getLookupId(),
                  cacheRequest.getLookupType(),
                  cacheRequest.getEnrichmentId(),
                  count,
                  cacheRequest.getLookupType(),
                  lastTimestamp);
              pullCheckpointService.setDynamicLookupPullCheckPoint(
                  tenantDataPlane.getTenantId().toString(),
                  tenantDataPlane.getDataPlaneId().toString(),
                  cacheRequest.getLookupType(),
                  cacheRequest.getLookupId().toString(),
                  enrichmentIdStr,
                  lastTimestamp);
            }
          } catch (Exception e) {
            log.error("Failed to process dynamic lookup cache request:" + cacheRequest.getId(), e);
          }
        }
      }
    } catch (Exception e) {
      log.error("Failed to pull dynamic lookup cache", e);
    } finally {
      lockService.unlock(LOCK_PULL_DYNAMIC_LOOKUP_CACHE_FLAGS, lockId);
    }
  }

  @Scheduled(cron = "30 */2 * * * *")
  public void pullLookupCache() {
    String lockId = UUID.randomUUID().toString();
    boolean locked = lockService.lock(LOCK_PULL_LOOKUP_CACHE_FLAGS, lockId, Duration.ofMinutes(6));
    if (!locked) {
      log.info("Failed to acquire lock for pulling lookup cache");
      return;
    }
    try {
      List<TenantDataPlane> tenantDataPlanes = dataPlaneConfig.getTenantDataPlanes();
      for (TenantDataPlane tenantDataPlane : tenantDataPlanes) {
        Response<List<LookupCacheRequest>> cacheRequestResponse =
            controlPlaneClient.getPendingLookupCacheRequests(
                tenantDataPlane.getTenantId().toString(),
                tenantDataPlane.getDataPlaneId().toString());
        if (cacheRequestResponse == null
            || cacheRequestResponse.getData() == null
            || cacheRequestResponse.getData().isEmpty()) {
          log.info(
              "No pending lookup cache requests for tenant {}, dpId: {}",
              tenantDataPlane.getTenantId(),
              tenantDataPlane.getDataPlaneId());
          continue;
        }
        List<LookupCacheRequest> cacheRequests = cacheRequestResponse.getData();
        for (LookupCacheRequest cacheRequest : cacheRequests) {
          log.info(
              "Processing lookup cache request lookupId:{}, requestId:{}, operation:{}, operationId:{}, lookupType: {}",
              cacheRequest.getLookupId(),
              cacheRequest.getId(),
              cacheRequest.getOperation(),
              cacheRequest.getOperationId(),
              cacheRequest.getLookupType());
          try {
            Object[] searchAfter = null;
            int count = 0;
            while (true) {
              CacheOperationRequest request = new CacheOperationRequest();
              request.setRequestId(cacheRequest.getId());
              request.setLookupOperations(cacheRequest.getOperation());
              request.setSize(1000);
              request.setSearchAfter(searchAfter);
              Response<CacheOperationDetails> operationDetails =
                  controlPlaneClient.getLookupCache(request);
              if (operationDetails == null
                  || operationDetails.getData() == null
                  || operationDetails.getData().getOperation() == null
                  || operationDetails.getData().getOperation().isEmpty()) {
                log.info(
                    "No more lookup cache operation for lookupId:{}, requestId:{}",
                    cacheRequest.getLookupId(),
                    cacheRequest.getId());
                break;
              }
              count += operationDetails.getData().getOperation().count();
              CacheOperation operation = operationDetails.getData().getOperation();
              performCacheOperation(cacheRequest.getLookupType(), operation);
              searchAfter = operationDetails.getData().getSearchAfter();
              if (searchAfter == null || searchAfter.length == 0) {
                break;
              }
            }
            log.info(
                "Lookup cache request lookupId:{}, requestId:{}, count: {} processed successfully for {}",
                cacheRequest.getLookupId(),
                cacheRequest.getId(),
                count,
                cacheRequest.getLookupType());
          } catch (Exception e) {
            log.error("Failed to process lookup cache request:" + cacheRequest.getId(), e);
            CacheOperationStatus status =
                CacheOperationStatus.failure(
                    cacheRequest.getId(),
                    tenantDataPlane.getTenantId(),
                    tenantDataPlane.getDataPlaneId(),
                    cacheRequest.getOperation(),
                    e.getMessage());
            controlPlaneClient.updateLookupCacheRequest(status);
          }
          CacheOperationStatus status =
              CacheOperationStatus.active(
                  cacheRequest.getId(),
                  tenantDataPlane.getTenantId(),
                  tenantDataPlane.getDataPlaneId(),
                  cacheRequest.getOperation());
          controlPlaneClient.updateLookupCacheRequest(status);
        }
      }
    } catch (Exception e) {
      log.error("Failed to pull lookup cache", e);
    } finally {
      lockService.unlock(LOCK_PULL_LOOKUP_CACHE_FLAGS, lockId);
    }
  }

  private void performCacheOperation(LookupType lookupType, CacheOperation operation) {
    CacheFunctionality functionality =
        switch (lookupType) {
          case ENRICHMENT -> CacheFunctionality.LOOKUP_ENRICHMENT;
          case VOLUME_CONTROLLER -> CacheFunctionality.LOOKUP_VOLUME_CONTROLLER;
        };
    CacheOperation.Operation action = operation.getOperation();
    switch (action) {
      case PUT -> {
        Map<String, String> putValues = operation.getPutValues();
        cacheService.multiSet(functionality, putValues);
      }
      case DEL -> {
        List<String> keysToDel = operation.getKeysToDel();
        for (String del : keysToDel) {
          cacheService.delete(functionality, del);
        }
      }
      case SET_ADD -> {
        String setKey = operation.getSetKey();
        Set<String> setValues = new HashSet<>(operation.getSetValues());
        cacheService.addInSet(functionality, setKey, setValues);
      }
      case SET_REMOVE -> {
        String setKey = operation.getSetKey();
        cacheService.removeSet(functionality, setKey);
      }
    }
  }
}
