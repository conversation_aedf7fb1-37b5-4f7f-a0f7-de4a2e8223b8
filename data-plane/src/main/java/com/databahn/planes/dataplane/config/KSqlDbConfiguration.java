package com.databahn.planes.dataplane.config;

import io.confluent.ksql.api.client.Client;
import io.confluent.ksql.api.client.ClientOptions;
import java.net.URL;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KSqlDbConfiguration {

  @Bean
  @SneakyThrows
  public Client kSqlDbClient(@Value("${urls.ksql_db}") String url) {
    URL kqlDbUrl = new URL(url);
    ClientOptions options =
        ClientOptions.create().setHost(kqlDbUrl.getHost()).setPort(kqlDbUrl.getPort());
    return Client.create(options);
  }
}
