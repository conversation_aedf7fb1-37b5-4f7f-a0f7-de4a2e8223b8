package com.databahn.planes.dataplane.model;

import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobRequest {
  private String id;
  private String action;
  private String source;
  private String destination;
  private String requestId;
  private String bucketName;
  private String bucketPrefix;
  private String accessKeyId;
  private String secretAccessKey;
  private String region;
  private String fileName;
  private String deviceType;
  private String deviceVendor;
  private String logType;
  private UUID fleetId;
  private UUID connectId;
  private UUID tenantId;
}
