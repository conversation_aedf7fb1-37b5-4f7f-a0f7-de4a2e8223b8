package com.databahn.planes.dataplane.service.kafkaconnet;

import com.databahn.planes.dataplane.client.KafkaConnectorClient;
import com.databahn.planes.dataplane.model.ConnectorPayload;
import com.databahn.planes.dataplane.model.KafkaConnectorInfo;
import com.databahn.planes.dataplane.model.PostConnectorPayload;
import com.databahn.planes.dataplane.processing.AckProducer;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.model.changeflag.ChangeFlag;
import feign.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Map;

import static com.databahn.planes.dataplane.constants.SnowFlakeConstants.*;

@Slf4j
@Service
@AllArgsConstructor
public class KafkaConnectorManagementService {

  private final KafkaConnectorClient kafkaConnectorClient;
  private final AckProducer ackProducer;

  private static final String OP_STOP = "STOP";
  private static final String OP_CREATE = "CREATE";
  private static final String OP_RESTART = "RESTART";
  private static final String OP_DELETE = "DELETE";
  private static final String OP_PAUSE = "PAUSE";
  private static final String OP_RESUME = "CREATE";

  void postToConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {

    URI uri = null;
    try {
      String connectorName = connectorPayload.getName();
      Response response =
          kafkaConnectorClient.createConnector(
              connectorPayload.getUri(),
              new PostConnectorPayload(connectorPayload.getName(), connectorPayload.getConfig()));
      genericResponseHandler(cf, entity, response, connectorName, OP_CREATE);

    } catch (Exception e) {
      log.error("Error while creating connector: {} " + connectorPayload.getName(), e.getMessage());
      e.printStackTrace();
      sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
    }
  }

  public void updateConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {
    String connectorName = connectorPayload.getName();
    URI uri = connectorPayload.getUri();
    KafkaConnectorInfo kafkaConnectorInfo =
        kafkaConnectorClient.getConnectorStatus(connectorPayload.getUri(), connectorName);
    if (kafkaConnectorInfo.getConnector().getState().equals(CONNECTOR_STATUS_STOPPED)) {
      try (Response resumeConnectorResponse =
          kafkaConnectorClient.resumeConnector(uri, connectorName)) {
        if (resumeConnectorResponse.status() == 200
            || resumeConnectorResponse.status() == 204
            || resumeConnectorResponse.status() == 202) {
          log.info(
              " Connector Resumed  Successfully : {}, {}",
              connectorName,
              resumeConnectorResponse.reason());
          sendAck(cf, ACK_STATUS_SUCCESS, connectorName, null);
        } else if (resumeConnectorResponse.status() == 404) {

          log.info(
              " Connector Does Not Exist  Creating One : {}, {}",
              connectorName,
              resumeConnectorResponse.reason());
          postToConnector(cf, entity, connectorPayload);
          log.info(
              " Connector Created Successfully : {}, {}",
              connectorName,
              resumeConnectorResponse.reason());
        } else {
          log.error("Error while Updating connector");
          sendAck(cf, ACK_STATUS_FAILURE, connectorName, "Error while Updating connector");
          log.error(
              "Connector Name : "
                  + connectorName
                  + " Error while Resuming connector error code: "
                  + resumeConnectorResponse.status()
                  + " Reason : "
                  + resumeConnectorResponse.reason());
        }
      } catch (Exception e) {
        log.error("Error while Updating connector:  " + connectorPayload.getName(), e.getMessage());
        sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
      }
    }

    try (Response response =
        kafkaConnectorClient.updateConnector(
            connectorPayload.getUri(), connectorName, connectorPayload.getConfig())) {
      log.info(" Connector Updated Config for Connector as Connector running - {}", connectorName);

      // CHECKING FOR RESPONSE DECIDE IF CONNECTOR UPDATED SUCCESSFULLY OR NOT
      if (response != null
          && (response.status() == 200 || response.status() == 204 || response.status() == 202)) {
        log.info(" Connector Updated  Successfully : {}, {}", connectorName, response.reason());
        sendAck(cf, ACK_STATUS_SUCCESS, connectorName, null);
      } else {
        log.error("Error while Updating connector");
        sendAck(cf, ACK_STATUS_FAILURE, connectorName, "Error while Updating connector");
        log.error(
            "Connector Name : "
                + connectorName
                + " Error while Updating connector error code: "
                + response.status()
                + " Reason : "
                + response.reason());
      }
    } catch (Exception e) {
      log.error("Error while Updating connector:  " + connectorPayload.getName(), e.getMessage());
      sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
    }
  }

  /**
   * This method is used to delete the connector based on the given entity. It deletes the connector
   * with the name .
   *
   * @param entity The entity used to delete the connector.
   */
  public void deleteConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {
    String connectorName = connectorPayload.getName();
    try (Response response =
        kafkaConnectorClient.deleteConnector(connectorPayload.getUri(), connectorName)) {
      genericResponseHandler(cf, entity, response, connectorName, OP_DELETE);
    } catch (Exception e) {
      log.error("Error while Delete  connector:  " + connectorPayload.getName(), e.getMessage());
      sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
    }
  }

  /**
   * This method is used to restart a specific connector. It restarts the connector with the name
   *
   * @param entity The entity used to restart the connector.
   */
  public void restartConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {
    String connectorName = connectorPayload.getName();

    genericResponseHandler(
        cf,
        entity,
        kafkaConnectorClient.restartConnector(connectorPayload.getUri(), connectorName),
        connectorName,
        OP_RESTART);
  }

  /**
   * This method is used to pause a specific connector. It pauses the connector with the name
   *
   * @param entity The entity used to pause the connector.
   */
  public void pauseConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {
    String connectorName = connectorPayload.getName();
    try (Response response =
        kafkaConnectorClient.pauseConnector(connectorPayload.getUri(), connectorName)) {
      genericResponseHandler(cf, entity, response, connectorName, OP_PAUSE);
    } catch (Exception e) {
      log.error("Error while Pause connector:  " + connectorPayload.getName(), e.getMessage());
      sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
    }
  }

  /**
   * This method is used to resume a specific connector. It resumes the connector with the name
   *
   * @param entity The entity used to resume the connector.
   */
  public void resumeConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {
    String connectorName = connectorPayload.getName();
    try (Response response =
        kafkaConnectorClient.resumeConnector(connectorPayload.getUri(), connectorName)) {
      genericResponseHandler(cf, entity, response, connectorName, OP_RESUME);
    } catch (Exception e) {
      log.error("Error while Resume connector:  " + connectorPayload.getName(), e.getMessage());
      sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
    }
  }

  /**
   * This method is used to stop a specific connector. It stops the connector with the name
   *
   * @param entity The entity used to stop the connector.
   */
  public void stopConnector(
      ChangeFlag cf, Map<String, Object> entity, ConnectorPayload connectorPayload) {
    String connectorName = connectorPayload.getName();
    try (Response response =
        kafkaConnectorClient.stopConnector(connectorPayload.getUri(), connectorName)) {
      genericResponseHandler(cf, entity, response, connectorName, OP_STOP);
    } catch (Exception e) {
      log.error("Error while Stop connector:  " + connectorPayload.getName(), e.getMessage());
      sendAck(cf, ACK_STATUS_FAILURE, connectorPayload.getName(), e.getMessage());
    }
  }

  public void sendAck(ChangeFlag cf, String status, String service, String error) {
    Ack ack = ackProducer.buildAck(cf, status, service, error);
    ackProducer.sendToKafkaSync(ack);
  }

  private void genericResponseHandler(
      ChangeFlag cf,
      Map<String, Object> entity,
      Response response,
      String connectorName,
      String action) {
    log.debug("{}", response.body());
    if (response.status() == 200
        || response.status() == 201
        || response.status() == 204
        || response.status() == 202) {
      log.info(
          " Operation {} on Connector Performed  Successfully :, {}, {}",
          action,
          connectorName,
          response.reason());
      sendAck(cf, ACK_STATUS_SUCCESS, connectorName, null);
    } else {
      String errorMsg =
          "Error While Performing Operation On Connector : "
              + connectorName
              + "  action :"
              + action
              + " With Error Code : "
              + response.status()
              + " Reason : "
              + response.reason();
      sendAck(cf, ACK_STATUS_FAILURE, connectorName, errorMsg);

      log.error("Error While Performing Operation {} On Connector : {}", action, connectorName);
      log.error(
          "Connector Name : "
              + connectorName
              + "Error While Performing Operation "
              + action
              + " On Connector  With Error Code : "
              + +response.status()
              + " Reason : "
              + response.reason());
    }
  }
}
