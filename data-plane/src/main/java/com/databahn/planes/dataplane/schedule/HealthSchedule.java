package com.databahn.planes.dataplane.schedule;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.dataplane.config.DataPlaneConfig;
import com.databahn.planes.dataplane.data.LockService;
import com.databahn.planes.model.dataplane.DataPlane;
import com.databahn.planes.response.Response;
import java.time.Duration;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HealthSchedule {
  private static final String HEALTH_FLAGS = "health_flags";
  private DataPlaneConfig dataPlaneConfig;
  private ControlPlaneClient controlPlaneClient;
  private LockService lockService;

  public HealthSchedule(
      DataPlaneConfig dataPlaneConfig,
      ControlPlaneClient controlPlaneClient,
      LockService lockService) {
    this.dataPlaneConfig = dataPlaneConfig;
    this.controlPlaneClient = controlPlaneClient;
    this.lockService = lockService;
  }

  @Scheduled(cron = "0 * * * * *")
  public void reportHealth() {
    String lockId = UUID.randomUUID().toString();
    boolean locked = lockService.lock(HEALTH_FLAGS, lockId, Duration.ofMinutes(1));
    if (!locked) {
      log.info("Failed to acquire lock for pulling change flags");
      return;
    }
    try {
      UUID dataPlaneId = dataPlaneConfig.getDataPlaneId();
      DataPlane dataPlane = new DataPlane(dataPlaneId);
      Response<String> health = controlPlaneClient.health(dataPlane);
      log.info("Data Plane Health check reported with response: {}", health.getData());
    } catch (Exception e) {
      log.error("Error while reporting health", e);
    } finally {
      lockService.unlock(HEALTH_FLAGS, lockId);
    }
  }
}
