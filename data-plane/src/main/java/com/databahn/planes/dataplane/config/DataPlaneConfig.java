package com.databahn.planes.dataplane.config;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.dataplane.Tenant;
import com.databahn.planes.model.dataplane.TenantDataPlane;
import com.databahn.planes.response.Response;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class DataPlaneConfig {
  private final UUID dataPlaneId;
  private final ControlPlaneClient controlPlaneClient;

  public DataPlaneConfig(
      @Value("${dataplane.id}") UUID dataPlaneId, ControlPlaneClient controlPlaneClient) {
    this.dataPlaneId = dataPlaneId;
    this.controlPlaneClient = controlPlaneClient;
  }

  public UUID getDataPlaneId() {
    return dataPlaneId;
  }

  public List<TenantDataPlane> getTenantDataPlanes() {
    Response<List<Tenant>> tenants = controlPlaneClient.getTenants(this.dataPlaneId);
    if (tenants.getStatus() == Response.Status.SUCCESS) {
      return tenants.getData().stream()
          .map(tenant -> new TenantDataPlane(tenant.getTenantId(), this.dataPlaneId))
          .toList();
    }
    throw new RuntimeException("Failed to fetch tenants with error" + tenants.getMessage());
  }
}
