package com.databahn.planes.dataplane.config;

import com.databahn.planes.model.dataplane.DataPlane;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.RequestTemplate;
import feign.codec.EncodeException;
import feign.codec.Encoder;
import java.lang.reflect.Type;
import org.springframework.http.MediaType;

public class OctetStreamEncoder implements Encoder {

  private final Encoder defaultEncoder;
  private final ObjectMapper objectMapper = new ObjectMapper();

  public OctetStreamEncoder(Encoder defaultEncoder) {
    this.defaultEncoder = defaultEncoder;
  }

  @Override
  public void encode(Object object, Type bodyType, RequestTemplate template)
      throws EncodeException {
    try {
      if (object instanceof String && template.url().contains("/v1/metrics/")) {
        template.header("Content-Type", MediaType.APPLICATION_OCTET_STREAM_VALUE);
        template.body((String) object);
      } else if (object instanceof DataPlane) {
        // Special handling for DataPlane objects
        String json = objectMapper.writeValueAsString(object);
        template.header("Content-Type", "application/json");
        template.body(json);
      } else {
        defaultEncoder.encode(object, bodyType, template);
      }
    } catch (Exception e) {
      throw new EncodeException("Error encoding object: " + e.getMessage(), e);
    }
  }
}
