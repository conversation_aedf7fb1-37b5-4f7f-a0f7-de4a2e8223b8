package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.alerts.Alert;
import com.databahn.planes.model.alerts.Alerts;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AlertsConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  public AlertsConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_alerts_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.management.alerts"},
      concurrency = "${consumer.alerts.threads:2}")
  public void onAlerts(List<ConsumerRecord<String, String>> records) {
    log.debug("data_plane_alerts_consumer received {} alerts", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    List<Alert> alerts = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      try {
        Alert alert = objectMapper.readValue(value, Alert.class);
        alerts.add(alert);
      } catch (JsonProcessingException e) {
        log.error("failed to parse alert:" + value, e);
        throw new BatchListenerFailedException("failed to parse alert", e, batchFirstRecord);
      }
    }
    Alerts alertsCollected = new Alerts(alerts);
    try {
      Response<Object> response = planeClient.upsertAlerts(alertsCollected);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error("failed to send alerts to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error("send alerts to control plane partially failed: {}", response.getData());
      } else {
        log.info("{} alerts saved", alerts.size());
      }
    } catch (Exception e) {
      log.error("failed to send alerts to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send alerts to control plane", e, batchFirstRecord);
    }
  }
}
