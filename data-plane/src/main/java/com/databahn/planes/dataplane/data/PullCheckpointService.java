package com.databahn.planes.dataplane.data;

import com.databahn.planes.dataplane.model.CacheFunctionality;
import com.databahn.planes.dataplane.service.CacheService;
import com.databahn.planes.model.constants.LookupType;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class PullCheckpointService {

  private static final String CHANGE_FLAG_PULL_CHECKPOINT_KEY = "#change_flag#seq#";
  private static final String DYNAMIC_LOOKUP_PULL_CHECKPOINT_KEY = "#dynamic_lookup#";
  private CacheService cacheService;

  public void setPullChangeFlagCheckpoint(String tenantId, String dataPlaneId, Long checkpoint) {
    if (checkpoint == null) {
      log.error("Checkpoint is null, ignoring the request to set checkpoint");
      return;
    }
    cacheService.set(
        CacheFunctionality.PULL_CHECKPOINT,
        CHANGE_FLAG_PULL_CHECKPOINT_KEY + tenantId + "#" + dataPlaneId,
        Objects.toString(checkpoint),
        null);
  }

  public Long getPullChangeFlagCheckpoint(String tenantId, String dataPlaneId) {
    String checkpoint =
        cacheService.get(
            CacheFunctionality.PULL_CHECKPOINT,
            CHANGE_FLAG_PULL_CHECKPOINT_KEY + tenantId + "#" + dataPlaneId);
    return checkpoint == null ? null : Long.parseLong(checkpoint);
  }

  public Long getDynamicLookupPullCheckPoint(
      String tenantId,
      String dataPlaneId,
      LookupType lookupType,
      String lookupId,
      String enrichmentId) {
    String key =
        dynamicLookupCheckpointKey(tenantId, dataPlaneId, lookupType, lookupId, enrichmentId);
    String checkpoint = cacheService.get(CacheFunctionality.PULL_CHECKPOINT, key);
    return checkpoint == null ? null : Long.parseLong(checkpoint);
  }

  public void setDynamicLookupPullCheckPoint(
      String tenantId,
      String dataPlaneId,
      LookupType lookupType,
      String lookupId,
      String enrichmentId,
      Long checkpoint) {
    String key =
        dynamicLookupCheckpointKey(tenantId, dataPlaneId, lookupType, lookupId, enrichmentId);
    cacheService.set(CacheFunctionality.PULL_CHECKPOINT, key, checkpoint.toString(), null);
  }

  private static String dynamicLookupCheckpointKey(
      String tenantId,
      String dataPlaneId,
      LookupType lookupType,
      String lookupId,
      String enrichmentId) {
    String opKey = lookupType == LookupType.ENRICHMENT ? lookupId + "/" + enrichmentId : lookupId;
    return DYNAMIC_LOOKUP_PULL_CHECKPOINT_KEY
        + tenantId
        + "#"
        + dataPlaneId
        + "#"
        + lookupType
        + "#"
        + opKey;
  }
}
