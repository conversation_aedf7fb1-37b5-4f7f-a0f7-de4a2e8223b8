package com.databahn.planes.dataplane.service.kafkaconnet;

import com.databahn.planes.dataplane.model.ConnectorPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.databahn.planes.dataplane.constants.SnowFlakeConstants.*;

@Slf4j
@Service
public class SnowFlakeConnector implements KafkaConnector {
  private static final String URL = "http://snowflake-dispenser.dispenser.svc.cluster.local:8080";

  @Override
  public ConnectorPayload getPayload(Map<String, Object> entity) {
    ConnectorPayload connectorPayload = new ConnectorPayload();
    String id = entity.get("id").toString();
    String topic = SNOWFLAKE_TOPIC_PREFIX + id + ".0";
    Map<String, String> config = (Map<String, String>) entity.get("config");
    Map<String, Object> payloadConfig = buildConfig(config, topic);
    connectorPayload.setTopic(topic);
    connectorPayload.setName(SNOWFLAKE_CONNECTOR_NAME_PREFIX + id);
    connectorPayload.setConfig(payloadConfig);
    connectorPayload.buildURI(URL);
    return connectorPayload;
  }

  @Override
  public Map<String, Object> buildConfig(Map<String, String> destinationConfig, String topic) {
    Map<String, Object> config = new HashMap<>();

    config.put(TOPICS_REGEX, topic + REGEX_STAR);
    config.put(
        SNOWFLAKE_TOPIC2TABLE_MAP,
        topic + COLON + destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_TABLE_NAME));
    config.put(SNOWFLAKE_URL_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_URL_NAME));
    config.put(SNOWFLAKE_USER_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_USER_NAME));
    config.put(SNOWFLAKE_ROLE_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_ROLE_NAME));
    config.put(
        SNOWFLAKE_PRIVATE_KEY, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_PRIVATE_KEY));
    config.put(
        SNOWFLAKE_DATABASE_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_DATABASE_NAME));
    config.put(
        SNOWFLAKE_SCHEMA_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_SCHEMA_NAME));
    config.put(TASKS_MAX, 3);
    config.put(CONNECTOR_CLASS, CONNECTOR_CLASS_SNOWFLAKE);
    config.put(SCHEMAS_ENABLE, Boolean.FALSE);
    config.put(BEHAVIOR_ON_NULL_VALUES, DEFAULT);
    config.put(KEY_CONVERTER, JSON_CONVERTER_CLASS);
    config.put(VALUE_CONVERTER, JSON_CONVERTER_CLASS);
    config.put(KEY_CONVERTER_SCHEMAS_ENABLE, Boolean.FALSE);
    config.put(VALUE_CONVERTER_SCHEMAS_ENABLE, Boolean.FALSE);
    config.put(SNOWFLAKE_INGESTION_METHOD, SNOWFLAKE_INGESTION_METHOD_SNOWPIPE_STREAMING);
    config.put(SNOWFLAKE_ENABLE_SCHEMATIZATION, Boolean.TRUE);
    config.put(ERRORS_TOLERANCE, ALL);
    config.put(ERRORS_LOG_ENABLE, Boolean.TRUE);
    config.put(ERRORS_LOG_INCLUDE_MESSAGES, Boolean.TRUE);
    config.put(BUFFER_COUNT_RECORDS, 15000);
    config.put(BUFFER_FLUSH_TIME, 60);
    config.put(BUFFER_SIZE_BYTES, 5000000);

    return config;
  }
}
