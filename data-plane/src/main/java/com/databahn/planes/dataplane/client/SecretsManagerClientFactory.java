package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.config.ControlPlaneTokens;
import com.databahn.planes.dataplane.config.OAuth2FeignRequestInterceptor;
import com.databahn.planes.dataplane.config.SecurityCommonUtils;
import feign.Client;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.security.oauth2.client.*;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SecretsManagerClientFactory {
  private Map<String, SecretsManagerClient> clients;

  public SecretsManagerClientFactory(
      ControlPlaneTokens controlPlaneTokens,
      ApplicationContext applicationContext,
      @Qualifier("feignProxyClient") Client feignClient,
      @Value("${urls.databahn_app}") String backendUrl,
      @Value("${spring.security.oauth2.client.provider.controlplane-client.token-uri}")
          String tokenUrl,
      @Value("${HTTPS_PROXY:#{null}}") String httpsProxy) {
    FeignClientBuilder feignClientBuilder = new FeignClientBuilder(applicationContext);
    this.clients = new HashMap<>();

    ClientRegistration[] clientRegistrations =
        controlPlaneTokens.getControlPlane().stream()
            .map(
                controlPlaneToken ->
                    ClientRegistration.withRegistrationId(controlPlaneToken.getClientId())
                        .clientId(controlPlaneToken.getClientId())
                        .clientSecret(controlPlaneToken.getClientSecret())
                        .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                        .tokenUri(tokenUrl)
                        .build())
            .toArray(ClientRegistration[]::new);
    InMemoryClientRegistrationRepository clientRegistrationsRepo =
        new InMemoryClientRegistrationRepository(clientRegistrations);
    InMemoryOAuth2AuthorizedClientService authorizedClientService =
        new InMemoryOAuth2AuthorizedClientService(clientRegistrationsRepo);
    OAuth2AuthorizedClientManager oAuth2ClientManager =
        SecurityCommonUtils.builFeignOAuth2AuthorizedClientManager(
            clientRegistrationsRepo, authorizedClientService, httpsProxy);

    for (ControlPlaneTokens.ControlPlaneToken controlPlaneToken :
        controlPlaneTokens.getControlPlane()) {
      FeignClientBuilder.Builder<SecretsManagerClient> clientBuilder =
          feignClientBuilder
              .forType(SecretsManagerClient.class, "secretsManagerClient")
              .customize(customizer -> customizer.client(feignClient))
              .url("https://" + backendUrl + "/v2")
              .customize(
                  customizer ->
                      customizer.requestInterceptor(
                          new OAuth2FeignRequestInterceptor(
                              controlPlaneToken.getClientId(), oAuth2ClientManager)));
      clients.put(controlPlaneToken.getTenantId(), clientBuilder.build());
    }
  }

  public Optional<SecretsManagerClient> getClient(String tenantId) {
    return Optional.ofNullable(clients.get(tenantId));
  }
}
