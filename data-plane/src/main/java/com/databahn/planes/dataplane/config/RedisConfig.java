package com.databahn.planes.dataplane.config;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Slf4j
@Configuration
public class RedisConfig {

  private final String redisUrl;

  public RedisConfig(@Value("${urls.redis}") String redisUrl) {
    this.redisUrl = redisUrl;
  }

  @Bean
  public RedisTemplate<String, String> redisTemplate() {
    RedisTemplate<String, String> template = new RedisTemplate<>();
    RedisConnectionFactory connectionFactory = jedisConnectionFactory();
    template.setConnectionFactory(connectionFactory);
    template.setKeySerializer(new StringRedisSerializer());
    template.setValueSerializer(new StringRedisSerializer());
    return template;
  }

  @SneakyThrows
  private RedisConnectionFactory jedisConnectionFactory() {
    log.info("connecting to redisUrl: {}", redisUrl);
    String[] parts = redisUrl.split(":");
    String host = parts[0];
    int port = Integer.parseInt(parts[1]);
    RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration(host, port);
    LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(configuration);
    connectionFactory.afterPropertiesSet();
    return connectionFactory;
  }
}
