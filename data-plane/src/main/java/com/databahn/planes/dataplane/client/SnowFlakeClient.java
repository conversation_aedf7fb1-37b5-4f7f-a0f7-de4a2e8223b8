package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.model.ConnectorPayload;
import com.databahn.planes.dataplane.model.KafkaConnectorInfo;
import feign.Response;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * The SnowFlakeClient interface defines the contract for interacting with the Snowflake service. It
 * provides methods to create, update, delete, restart, pause, resume, and stop connectors. This
 * interface is annotated with @FeignClient, which allows it to send HTTP requests to the Snowflake
 * service.
 */
@FeignClient(
    value = "snowflake-client",
    url =
        "${urls.snowflake_service_url:http://snowflake-dispenser.dispenser.svc.cluster.local:8080}")
public interface SnowFlakeClient {

  /**
   * This method is used to create a new connector with the given payload.
   *
   * @param payload The payload for the new connector.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.POST, value = "/connectors")
  Response createConnector(@RequestBody ConnectorPayload payload);

  /**
   * This method is used to update an existing connector with the given configuration.
   *
   * @param config The new configuration for the connector.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.PUT, value = "/connectors/{connectorName}/config")
  Response updateConnector(
      @PathVariable("connectorName") String connectorName, @RequestBody Map<String, Object> config);

  /**
   * This method is used to delete a specific connector.
   *
   * @param connectorName The name of the connector to delete.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.DELETE, value = "/connectors/{connectorName}")
  Response deleteConnector(@PathVariable("connectorName") String connectorName);

  /**
   * This method is used to restart a specific connector.
   *
   * @param connectorName The name of the connector to restart.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(
      method = RequestMethod.POST,
      value = "/connectors/{connectorName}/restart",
      consumes = "application/json")
  Response restartConnector(@PathVariable("connectorName") String connectorName);

  /**
   * This method is used to pause a specific connector.
   *
   * @param connectorName The name of the connector to pause.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.PUT, value = "/connectors/{connectorName}/pause")
  Response pauseConnector(@PathVariable("connectorName") String connectorName);

  /**
   * This method is used to resume a specific connector.
   *
   * @param connectorName The name of the connector to resume.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.PUT, value = "/connectors/{connectorName}/resume")
  Response resumeConnector(@PathVariable("connectorName") String connectorName);

  /**
   * This method is used to stop a specific connector.
   *
   * @param connectorName The name of the connector to stop.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.PUT, value = "/connectors/{connectorName}/stop")
  Response stopConnector(@PathVariable("connectorName") String connectorName);

  /**
   * This method is used to Status a specific connector.
   *
   * @param connectorName The name of the connector to get Status.
   * @return The response from the Snowflake service.
   */
  @RequestMapping(method = RequestMethod.GET, value = "/connectors/{connectorName}/status")
  KafkaConnectorInfo getConnectorStatus(@PathVariable("connectorName") String connectorName);
}
