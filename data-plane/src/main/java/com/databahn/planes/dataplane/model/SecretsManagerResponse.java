package com.databahn.planes.dataplane.model;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class SecretsManagerResponse {
  private String id;
  private String name;
  private String valueType;
  private Object value;
  private int version;

  public Map<String, String> getValueForJson() {
    if (value == null) {
      return null;
    }
    if (!(value instanceof Map)) {
      throw new RuntimeException("SecretsManagerResponse value for JSON is not a Map");
    }
    Map<String, String> result = new HashMap<>();
    Map<String, Object> map = (Map<String, Object>) value;
    for (Map.Entry<String, Object> entry : map.entrySet()) {
      result.put(entry.getKey(), String.valueOf(entry.getValue()));
    }
    return result;
  }

  public String getValueForString() {
    return String.valueOf(value);
  }

  public enum ValueType {
    JSON,
    STRING
  }
}
