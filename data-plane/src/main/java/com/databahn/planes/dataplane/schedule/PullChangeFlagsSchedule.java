package com.databahn.planes.dataplane.schedule;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.dataplane.config.DataPlaneConfig;
import com.databahn.planes.dataplane.data.LockService;
import com.databahn.planes.dataplane.data.PullCheckpointService;
import com.databahn.planes.dataplane.service.ChangeFlagService;
import com.databahn.planes.dataplane.service.KafkaAdminService;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.databahn.planes.model.dataplane.TenantDataPlane;
import com.databahn.planes.response.Response;
import java.time.Duration;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PullChangeFlagsSchedule {

  private static final String LOCK_PULL_CHANGE_FLAGS = "pull_change_flags";
  private DataPlaneConfig dataPlaneConfig;
  private ControlPlaneClient controlPlaneClient;
  private ChangeFlagService changeFlagService;
  private PullCheckpointService pullCheckpointService;
  private LockService lockService;
  private KafkaAdminService kafkaAdminService;

  public PullChangeFlagsSchedule(
      DataPlaneConfig dataPlaneConfig,
      ControlPlaneClient controlPlaneClient,
      ChangeFlagService changeFlagService,
      PullCheckpointService pullCheckpointService,
      LockService lockService,
      KafkaAdminService kafkaAdminService) {
    this.dataPlaneConfig = dataPlaneConfig;
    this.controlPlaneClient = controlPlaneClient;
    this.changeFlagService = changeFlagService;
    this.pullCheckpointService = pullCheckpointService;
    this.lockService = lockService;
    this.kafkaAdminService = kafkaAdminService;
    log.info("PullChangeFlagsSchedule initialized");
  }

  @Scheduled(cron = "0/30 * * * * *")
  public void pullChangeFlags() {
    String lockId = UUID.randomUUID().toString();
    boolean locked = lockService.lock(LOCK_PULL_CHANGE_FLAGS, lockId, Duration.ofMinutes(1));
    if (!locked) {
      log.info("Failed to acquire lock for pulling change flags");
      return;
    }
    try {
      List<TenantDataPlane> tenantDataPlanes = dataPlaneConfig.getTenantDataPlanes();
      if (tenantDataPlanes.isEmpty()) {
        log.error("No TenantIds, ignoring the request to pull change flags");
        return;
      }
      for (TenantDataPlane tenantDataPlane : tenantDataPlanes) {
        pullChangeFlagsAndProcess(tenantDataPlane);
      }
    } catch (Exception e) {
      log.error("Failed to pull change flags", e);
    } finally {
      lockService.unlock(LOCK_PULL_CHANGE_FLAGS, lockId);
    }
  }

  private void pullChangeFlagsAndProcess(TenantDataPlane tenantDataPlane) {
    String tenantId = tenantDataPlane.getTenantId().toString();
    String dataPlaneId = tenantDataPlane.getDataPlaneId().toString();
    Long checkpoint = pullCheckpointService.getPullChangeFlagCheckpoint(tenantId, dataPlaneId);
    int size = 10;
    while (true) {
      Response<List<ChangeFlag>> changeFlags;
      try {
        changeFlags =
            controlPlaneClient.getChangeFlags(
                tenantId,
                dataPlaneId,
                checkpoint,
                0,
                size); // page size 0 because we are using checkpoint condition
      } catch (Exception e) {
        log.error("Failed to pull change flags for tenantId: {}", tenantId, e);
        return;
      }
      if (changeFlags == null || changeFlags.getData() == null || changeFlags.getData().isEmpty()) {
        log.info(
            "No change flags to pull @ checkpoint: {}, tenantId: {}, dpId: {}",
            checkpoint,
            tenantDataPlane.getTenantId(),
            tenantDataPlane.getDataPlaneId());
        break;
      } else {
        List<ChangeFlag> changeFlagList = changeFlags.getData();
        Long newCheckpoint =
            changeFlagList.stream().map(ChangeFlag::sequenceId).max(Long::compareTo).get();
        kafkaAdminService.processChangeFlag(changeFlagList);
        changeFlagService.sendSync(changeFlagList);
        log.info(
            "Pulled {} change flags successfully, for checkpoint: {}, new checkpoint: {}, tenantId: {}, dpId: {}",
            changeFlagList.size(),
            checkpoint,
            newCheckpoint,
            tenantDataPlane.getTenantId(),
            tenantDataPlane.getDataPlaneId());
        pullCheckpointService.setPullChangeFlagCheckpoint(tenantId, dataPlaneId, newCheckpoint);
        checkpoint = newCheckpoint;
      }
    }
  }
}
