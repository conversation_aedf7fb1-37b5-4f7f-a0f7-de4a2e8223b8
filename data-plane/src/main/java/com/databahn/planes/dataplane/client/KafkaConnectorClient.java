package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.model.KafkaConnectorInfo;
import com.databahn.planes.dataplane.model.PostConnectorPayload;
import feign.Response;
import java.net.URI;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * The KafkaConnectClient interface defines the contract for interacting with the Kafka Connect
 * services. It provides methods to create, update, delete, restart, pause, resume, and stop
 * connectors. This interface is annotated with @FeignClient, which allows it to send HTTP requests
 * to the Snowflake service.
 */
@FeignClient(value = "kafka-connect-client", url = "https://this-is-a-placeholder.com")
public interface KafkaConnectorClient {

  /**
   * This method is used to create a new connector with the given payload.
   *
   * @param payload The payload for the new connector.
   * @return The response from the Connector(Source)/(Sink) Dispenser service.
   */
  @PostMapping(path = "/connectors")
  Response createConnector(URI baseUrl, @RequestBody PostConnectorPayload payload);

  /**
   * This method is used to update an existing Connector(Source)/(Sink) with the given
   * configuration.
   *
   * @param config The new configuration for the connector.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @PutMapping(path = "/connectors/{connectorName}/config")
  Response updateConnector(
      URI baseUrl,
      @PathVariable("connectorName") String connectorName,
      @RequestBody Map<String, Object> config);

  /**
   * This method is used to delete a specific Connector(Source)/(Sink).
   *
   * @param connectorName The name of the Connector(Source)/(Sink) to delete.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @DeleteMapping(path = "/connectors/{connectorName}")
  Response deleteConnector(URI baseUrl, @PathVariable("connectorName") String connectorName);

  /**
   * This method is used to restart a specific Connector(Source)/(Sink).
   *
   * @param connectorName The name of the Connector(Source)/(Sink) to restart.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @PostMapping(path = "/connectors/{connectorName}/restart", consumes = "application/json")
  Response restartConnector(URI baseUrl, @PathVariable("connectorName") String connectorName);

  /**
   * This method is used to pause a specific Connector(Source)/(Sink).
   *
   * @param connectorName The name of the Connector(Source)/(Sink) to pause.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @PutMapping(path = "/connectors/{connectorName}/pause")
  Response pauseConnector(URI baseUrl, @PathVariable("connectorName") String connectorName);

  /**
   * This method is used to resume a specific Connector(Source)/(Sink).
   *
   * @param connectorName The name of the Connector(Source)/(Sink) to resume.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @PutMapping(path = "/connectors/{connectorName}/resume")
  Response resumeConnector(URI baseUrl, @PathVariable("connectorName") String connectorName);

  /**
   * This method is used to stop a specific Connector(Source)/(Sink).
   *
   * @param connectorName The name of the Connector(Source)/(Sink) to stop.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @PutMapping(path = "/connectors/{connectorName}/stop")
  Response stopConnector(URI baseUrl, @PathVariable("connectorName") String connectorName);

  /**
   * This method is used to Status a specific Connector(Source)/(Sink).
   *
   * @param connectorName The name of the Connector(Source)/(Sink) to get Status.
   * @return The response from the Connector(Source)/(Sink) service.
   */
  @GetMapping(path = "/connectors/{connectorName}/status")
  KafkaConnectorInfo getConnectorStatus(
      URI baseUrl, @PathVariable("connectorName") String connectorName);
}
