package com.databahn.planes.dataplane.config;

import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.Proxy;
import java.net.URL;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.ClientCredentialsOAuth2AuthorizedClientProvider;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.endpoint.DefaultClientCredentialsTokenResponseClient;
import org.springframework.security.oauth2.client.http.OAuth2ErrorResponseErrorHandler;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.http.converter.OAuth2AccessTokenResponseHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class SecurityCommonUtils {

  public static OAuth2AuthorizedClientManager builFeignOAuth2AuthorizedClientManager(
      ClientRegistrationRepository clientRegistrationRepository,
      OAuth2AuthorizedClientService oAuth2AuthorizedClientService,
      String httpsProxy) {
    AuthorizedClientServiceOAuth2AuthorizedClientManager oAuthClientManager =
        new AuthorizedClientServiceOAuth2AuthorizedClientManager(
            clientRegistrationRepository, oAuth2AuthorizedClientService);
    if (!StringUtils.isEmpty(httpsProxy)) {
      URL url = proxyUrl(httpsProxy);
      String host = url.getHost();
      int port = url.getPort();
      SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
      requestFactory.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port)));
      RestTemplate restTemplate =
          new RestTemplate(
              Arrays.asList(
                  new FormHttpMessageConverter(),
                  new OAuth2AccessTokenResponseHttpMessageConverter()));
      restTemplate.setErrorHandler(new OAuth2ErrorResponseErrorHandler());
      restTemplate.setRequestFactory(requestFactory);
      DefaultClientCredentialsTokenResponseClient tokenResponseClient =
          new DefaultClientCredentialsTokenResponseClient();
      tokenResponseClient.setRestOperations(restTemplate);
      ClientCredentialsOAuth2AuthorizedClientProvider authorizedClientProvider =
          new ClientCredentialsOAuth2AuthorizedClientProvider();
      authorizedClientProvider.setAccessTokenResponseClient(tokenResponseClient);
      oAuthClientManager.setAuthorizedClientProvider(authorizedClientProvider);
      log.info("Feign client auth configured with proxy host: {} and port: {}", host, port);
    }
    return oAuthClientManager;
  }

  private static URL proxyUrl(String httpsProxy) {
    try {
      return new URL(httpsProxy);
    } catch (MalformedURLException e) {
      throw new RuntimeException(e);
    }
  }
}
