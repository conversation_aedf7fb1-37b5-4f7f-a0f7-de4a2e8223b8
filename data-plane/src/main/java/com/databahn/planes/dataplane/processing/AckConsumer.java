package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AckConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  public AckConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_ack_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.management.ack"},
      concurrency = "${consumer.ack.threads:1}")
  public void onAck(List<ConsumerRecord<String, String>> records) {
    log.debug("data_plane_ack_consumer received {} acks", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    List<Ack> acks = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      try {
        Ack ack = objectMapper.readValue(value, Ack.class);
        acks.add(ack);
      } catch (JsonProcessingException e) {
        log.error("failed to parse ack:" + value, e);
        throw new BatchListenerFailedException("failed to parse ack", e, batchFirstRecord);
      }
    }
    try {
      Response<Object> response = planeClient.saveAcknowledgements(acks);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error("failed to send acks to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error("send acks to control plane partially failed: {}", response.getData());
      } else {
        log.info("{} acks saved", acks.size());
      }
    } catch (Exception e) {
      log.error("failed to send acks to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send acks to control plane", e, batchFirstRecord);
    }
  }
}
