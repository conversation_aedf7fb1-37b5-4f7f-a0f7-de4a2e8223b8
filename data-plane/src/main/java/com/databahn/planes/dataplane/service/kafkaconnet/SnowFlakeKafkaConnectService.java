package com.databahn.planes.dataplane.service.kafkaconnet;

import com.databahn.planes.dataplane.client.SnowFlakeClient;
import com.databahn.planes.dataplane.model.ConnectorPayload;
import com.databahn.planes.dataplane.model.KafkaConnectorInfo;
import com.databahn.planes.exception.KafkaConnectFailureException;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.model.ack.AckTypes;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.databahn.planes.dataplane.constants.SnowFlakeConstants.*;

/**
 * This service class is responsible for managing the Snowflake Kafka Connectors. It provides
 * methods to create, update, and delete connectors based on the provided change flags. It also
 * builds the configuration for the connectors based on the destination configuration and
 * topic. @Author: <PERSON><PERSON> (NC)
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SnowFlakeKafkaConnectService {

  private final SnowFlakeClient snowFlakeClient;

  /**
   * This method is used to post the changeFlag to the connector. It creates a connector with the
   * given changeFlag.
   *
   * @param changeFlag The changeFlag to be posted to the connector.
   */
  public void postToConnector(Map<String, Object> changeFlag) {
    try {
      String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();
      Response response = snowFlakeClient.createConnector(getPayload(changeFlag));
      if (response.status() == 201) {
        log.info("Connector Created  Successfully : {}, {}", connectorName, response.reason());
        sendAck(changeFlag, ACK_STATUS_SUCCESS);
      } else {
        log.error("Error while creating connector");
        sendAck(changeFlag, ACK_STATUS_FAILURE);
        throw new KafkaConnectFailureException(
            "Connector Name : "
                + connectorName
                + " Error while creating connector error code: "
                + response.status()
                + " Reason : "
                + response.reason());
      }
    } catch (Exception e) {
      log.error("Error while creating connector: {}", e.getMessage());
    }
  }

  /**
   * This method is used to get the payload for the connector. It builds the payload based on the
   * given changeFlag.
   *
   * @param changeFlag The changeFlag used to build the payload.
   * @return The payload for the connector.
   */
  private ConnectorPayload getPayload(Map<String, Object> changeFlag) {
    String id = changeFlag.get("id").toString();
    String topic = "db.destination.snowflake." + id + ".0";
    Map<String, String> config = (Map<String, String>) changeFlag.get("config");
    Map<String, Object> payloadConfig = buildConfig(config, topic);
    ConnectorPayload connectorPayload = new ConnectorPayload();
    connectorPayload.setName("destination_snowflake_" + id);
    connectorPayload.setConfig(payloadConfig);
    return connectorPayload;
  }

  /**
   * This method is used to update the configuration of a specific Snowflake Kafka Connector based
   * on the provided change flag. The connector is identified by the name "destination_snowflake_"
   * concatenated with the "id" from the change flag. If the connector's current state is "STOPPED",
   * it resumes the connector before updating its configuration. After ensuring that the connector
   * is running, it updates the connector's configuration.
   *
   * @param changeFlag The change flag containing the updated configuration for the connector.
   * @throws KafkaConnectFailureException If there is an error while resuming or updating the
   *     connector.
   */
  public void updateConnector(Map<String, Object> changeFlag) {
    String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();

    KafkaConnectorInfo kafkaConnectorInfo = snowFlakeClient.getConnectorStatus(connectorName);
    if (kafkaConnectorInfo.getConnector().getState().equals(CONNECTOR_STATUS_STOPPED)) {
      Response resumeConnectorResponse = snowFlakeClient.resumeConnector(connectorName);
      if (resumeConnectorResponse.status() == 200
          || resumeConnectorResponse.status() == 204
          || resumeConnectorResponse.status() == 202) {
        log.info(
            " Connector Resumed  Successfully : {}, {}",
            connectorName,
            resumeConnectorResponse.reason());
        sendAck(changeFlag, ACK_STATUS_SUCCESS);
      } else if (resumeConnectorResponse.status() == 404) {

        log.info(
            " Connector Does Not Exist  Creating One : {}, {}",
            connectorName,
            resumeConnectorResponse.reason());
        postToConnector(changeFlag);
        log.info(
            " Connector Created Successfully : {}, {}",
            connectorName,
            resumeConnectorResponse.reason());
      } else {
        log.error("Error while Updating connector");
        sendAck(changeFlag, ACK_STATUS_FAILURE);
        throw new KafkaConnectFailureException(
            "Connector Name : "
                + connectorName
                + " Error while Resuming connector error code: "
                + resumeConnectorResponse.status()
                + " Reason : "
                + resumeConnectorResponse.reason());
      }
    }

    Response response =
        snowFlakeClient.updateConnector(connectorName, getPayload(changeFlag).getConfig());
    log.info(" Connector Updated Config for Connector as Connector running - {}", connectorName);

    // CHECKING FOR RESPONSE DECIDE IF CONNECTOR UPDATED SUCCESSFULLY OR NOT
    if (response != null
        && (response.status() == 200 || response.status() == 204 || response.status() == 202)) {
      log.info(" Connector Updated  Successfully : {}, {}", connectorName, response.reason());
      sendAck(changeFlag, ACK_STATUS_SUCCESS);
    } else {
      log.error("Error while Updating connector");
      sendAck(changeFlag, ACK_STATUS_FAILURE);
      throw new KafkaConnectFailureException(
          "Connector Name : "
              + connectorName
              + " Error while Updating connector error code: "
              + response.status()
              + " Reason : "
              + response.reason());
    }
  }

  /**
   * This method is used to delete the connector based on the given changeFlag. It deletes the
   * connector with the name "destination_snowflake_" + changeFlag.get("id").
   *
   * @param changeFlag The changeFlag used to delete the connector.
   */
  public void deleteConnector(Map<String, Object> changeFlag) {
    String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();
    Response response = snowFlakeClient.deleteConnector(connectorName);
    if (response.status() == 200 || response.status() == 204 || response.status() == 202) {
      sendAck(changeFlag, ACK_STATUS_SUCCESS);
      log.info(" Connector Deleted  Successfully : {}, {}", connectorName, response.reason());
    } else {
      sendAck(changeFlag, ACK_STATUS_FAILURE);
      log.error("Error while Deleting connector");
      throw new KafkaConnectFailureException(
          "Connector Name : "
              + connectorName
              + " Error while  Deleting connector error code: "
              + response.status()
              + " Reason : "
              + response.reason());
    }
  }

  /**
   * This method is used to restart a specific connector. It restarts the connector with the name
   * "destination_snowflake_" + changeFlag.get("id").
   *
   * @param changeFlag The changeFlag used to restart the connector.
   */
  public void restartConnector(Map<String, Object> changeFlag) {
    String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();

    Response response = snowFlakeClient.restartConnector(connectorName);
    if (response.status() == 200 || response.status() == 204 || response.status() == 202) {
      log.info(" Connector Restarted  Successfully : {}, {}", connectorName, response.reason());
      sendAck(changeFlag, ACK_STATUS_SUCCESS);
    } else {
      log.error("Error while Restarting connector");
      sendAck(changeFlag, ACK_STATUS_FAILURE);
      throw new KafkaConnectFailureException(
          "Connector Name : "
              + connectorName
              + " Error while  Restarting connector error code: "
              + response.status()
              + " Reason : "
              + response.reason());
    }
  }

  /**
   * This method is used to pause a specific connector. It pauses the connector with the name
   * "destination_snowflake_" + changeFlag.get("id").
   *
   * @param changeFlag The changeFlag used to pause the connector.
   */
  public void pauseConnector(Map<String, Object> changeFlag) {
    String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();
    Response response = snowFlakeClient.pauseConnector(connectorName);
    log.debug("{}", response.body());
    if (response.status() == 200 || response.status() == 204 || response.status() == 202) {
      log.info(" Connector Paused  Successfully : {}, {}", connectorName, response.reason());
      sendAck(changeFlag, ACK_STATUS_SUCCESS);
    } else {
      log.error("Error while Pausing connector");
      sendAck(changeFlag, ACK_STATUS_FAILURE);
      throw new KafkaConnectFailureException(
          "Connector Name : "
              + connectorName
              + " Error while  Pausing  connector error code: "
              + response.status()
              + " Reason : "
              + response.reason());
    }
  }

  /**
   * This method is used to resume a specific connector. It resumes the connector with the name
   * "destination_snowflake_" + changeFlag.get("id").
   *
   * @param changeFlag The changeFlag used to resume the connector.
   */
  public void resumeConnector(Map<String, Object> changeFlag) {
    String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();
    Response response = snowFlakeClient.resumeConnector(connectorName);
    log.debug("{}", response.body());
    if (response.status() == 200 || response.status() == 204 || response.status() == 202) {
      log.info(" Connector Resumed  Successfully : {}, {}", connectorName, response.reason());
      sendAck(changeFlag, ACK_STATUS_SUCCESS);
    } else {
      log.error("Error while Resuming connector");
      sendAck(changeFlag, ACK_STATUS_FAILURE);
      throw new KafkaConnectFailureException(
          "Connector Name : "
              + connectorName
              + " Error while Resuming connector error code: "
              + response.status()
              + " Reason : "
              + response.reason());
    }
  }

  /**
   * This method is used to stop a specific connector. It stops the connector with the name
   * "destination_snowflake_" + changeFlag.get("id").
   *
   * @param changeFlag The changeFlag used to stop the connector.
   */
  public void stopConnector(Map<String, Object> changeFlag) {
    String connectorName = "destination_snowflake_" + changeFlag.get("id").toString();
    Response response = snowFlakeClient.stopConnector(connectorName);
    log.debug("{}", response.body());
    if (response.status() == 200 || response.status() == 204 || response.status() == 202) {
      log.info(" Connector Stopped  Successfully : {}, {}", connectorName, response.reason());
      sendAck(changeFlag, ACK_STATUS_SUCCESS);
    } else {
      sendAck(changeFlag, ACK_STATUS_FAILURE);
      log.error("Error while Stopping connector");
      throw new KafkaConnectFailureException(
          "Connector Name : "
              + connectorName
              + " Error while Stopping connector error code: "
              + response.status()
              + " Reason : "
              + response.reason());
    }
  }

  /**
   * This method is used to build the configuration for the Snowflake Kafka Connector. It takes the
   * destination configuration and topic as input and returns a map of configuration key-value
   * pairs. The configuration includes various settings for the connector such as topics regex,
   * Snowflake URL, user name, role name, private key, database name, schema name, etc.
   *
   * @param destinationConfig The destination configuration map which includes settings specific to
   *     the destination.
   * @param topic The topic for which the connector is being configured.
   * @return A map of configuration key-value pairs for the Snowflake Kafka Connector.
   */
  private Map<String, Object> buildConfig(Map<String, String> destinationConfig, String topic) {
    Map<String, Object> config = new HashMap<>();

    config.put(TOPICS_REGEX, topic + REGEX_STAR);
    config.put(
        SNOWFLAKE_TOPIC2TABLE_MAP,
        topic + COLON + destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_TABLE_NAME));
    config.put(SNOWFLAKE_URL_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_URL_NAME));
    config.put(SNOWFLAKE_USER_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_USER_NAME));
    config.put(SNOWFLAKE_ROLE_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_ROLE_NAME));
    config.put(
        SNOWFLAKE_PRIVATE_KEY, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_PRIVATE_KEY));
    config.put(
        SNOWFLAKE_DATABASE_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_DATABASE_NAME));
    config.put(
        SNOWFLAKE_SCHEMA_NAME, destinationConfig.get(DESTINATION_CONFIG_SNOWFLAKE_SCHEMA_NAME));
    config.put(TASKS_MAX, 3);
    config.put(CONNECTOR_CLASS, CONNECTOR_CLASS_SNOWFLAKE);
    config.put(SCHEMAS_ENABLE, Boolean.FALSE);
    config.put(BEHAVIOR_ON_NULL_VALUES, DEFAULT);
    config.put(KEY_CONVERTER, JSON_CONVERTER_CLASS);
    config.put(VALUE_CONVERTER, JSON_CONVERTER_CLASS);
    config.put(KEY_CONVERTER_SCHEMAS_ENABLE, Boolean.FALSE);
    config.put(VALUE_CONVERTER_SCHEMAS_ENABLE, Boolean.FALSE);
    config.put(SNOWFLAKE_INGESTION_METHOD, SNOWFLAKE_INGESTION_METHOD_SNOWPIPE_STREAMING);
    config.put(SNOWFLAKE_ENABLE_SCHEMATIZATION, Boolean.TRUE);
    config.put(ERRORS_TOLERANCE, ALL);
    config.put(ERRORS_LOG_ENABLE, Boolean.TRUE);
    config.put(ERRORS_LOG_INCLUDE_MESSAGES, Boolean.TRUE);
    config.put(BUFFER_COUNT_RECORDS, 15000);
    config.put(BUFFER_FLUSH_TIME, 60);
    config.put(BUFFER_SIZE_BYTES, 5000000);

    return config;
  }

  public void sendAck(Map<String, Object> changeFlag, String status) {

    Ack ack = new Ack();
    ack.setId((String) changeFlag.get(ID));
    ack.setRequestId((String) changeFlag.get(REQUEST_ID));
    ack.setCustomerId((String) changeFlag.get(CUSTOMER_ID));
    ack.setType(AckTypes.CHANGE_FLAG);
    ack.setEntityId((String) changeFlag.get(ENTITY_ID));
    ack.setEntityType((String) changeFlag.get(ENTITY_TYPE));
    ack.setEntityVersion((String) changeFlag.get(ENTITY_VERSION));
    ack.setServiceName((String) changeFlag.get(SERVICE_NAME));
    ack.setTenantId((String) changeFlag.get(TENANT_ID));
    ack.setAction((String) changeFlag.get(ACTION));
    ack.setStatus(status);
    ack.setError((String) changeFlag.get(ERROR));
    ack.setProgress((String) changeFlag.get(PROGRESS));
  }
}
