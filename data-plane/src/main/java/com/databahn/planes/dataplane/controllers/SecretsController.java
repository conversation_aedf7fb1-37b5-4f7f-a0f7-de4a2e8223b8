package com.databahn.planes.dataplane.controllers;

import com.databahn.planes.dataplane.model.SecretsRequest;
import com.databahn.planes.dataplane.model.SecretsResponse;
import com.databahn.planes.dataplane.service.SecretsService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/internal/v1/secrets")
public class SecretsController {

  private SecretsService service;

  @PostMapping
  public SecretsResponse getSecrets(@RequestBody SecretsRequest request) {
    return service.getSecrets(request);
  }
}
