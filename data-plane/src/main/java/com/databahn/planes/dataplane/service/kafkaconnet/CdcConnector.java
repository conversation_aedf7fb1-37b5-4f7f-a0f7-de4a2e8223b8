package com.databahn.planes.dataplane.service.kafkaconnet;

import com.databahn.planes.dataplane.model.ConnectorPayload;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.databahn.planes.dataplane.constants.CdcConstants.*;

@Slf4j
@Service
@Data
public class CdcConnector implements KafkaConnector {
  private static final String URL = "http://cdc-connector.connector.svc.cluster.local:8080";
  // private final static String URL = "http://localhost:8001";
  @Value("${kafka.processing.bootstrap_brokers}")
  private String kafkaBrokers;

  @Override
  public ConnectorPayload getPayload(Map<String, Object> entity) {
    ConnectorPayload connectorPayload = new ConnectorPayload();
    String id = entity.get("id").toString();
    String name = "cdc_connector_" + id;
    Map<String, Object> payloadConfig = buildConfig(entity);
    connectorPayload.setTopic(null);
    connectorPayload.setName(name);
    payloadConfig.put(DATABASE_SERVER_ID, generate8DigitHash(name));
    connectorPayload.setConfig(payloadConfig);
    connectorPayload.setPartitions(1);
    connectorPayload.setReplicationFactor(1);
    connectorPayload.buildURI(URL);
    return connectorPayload;
  }

  public Map<String, Object> buildConfig(Map<String, Object> entity) {

    String id = entity.get("id").toString();

    String tenantId = entity.get("tenant_id").toString();
    String name = entity.get("name").toString();
    String topic = "db.cdc." + id + ".0";

    Map<String, String> config = (Map<String, String>) entity.get("config");
    Map<String, Object> payloadConfig = buildConfig(config, topic);

    // Additional Conditions
    // db_event_source_id Header
    payloadConfig.put(
        "transforms.db_event_source_id.type", "org.apache.kafka.connect.transforms.InsertHeader");
    payloadConfig.put("transforms.db_event_source_id.header", "db_event_source_id");
    payloadConfig.put("transforms.db_event_source_id.value.literal", id);

    // db_source_name Header
    payloadConfig.put(
        "transforms.db_source_name.type", "org.apache.kafka.connect.transforms.InsertHeader");
    payloadConfig.put("transforms.db_source_name.header", "db_source_name");
    payloadConfig.put("transforms.db_source_name.value.literal", name);

    // db_tenant_id Header
    payloadConfig.put(
        "transforms.db_tenant_id.type", "org.apache.kafka.connect.transforms.InsertHeader");
    payloadConfig.put("transforms.db_tenant_id.header", "db_tenant_id");
    payloadConfig.put("transforms.db_tenant_id.value.literal", tenantId);

    return payloadConfig;
  }

  @Override
  public Map<String, Object> buildConfig(Map<String, String> entityConfig, String topic) {
    Map<String, Object> connectConfig = new HashMap<>();
    connectConfig.put(CONNECTOR_CLASS, "io.debezium.connector.mysql.MySqlConnector");
    connectConfig.put(TOPIC_PREFIX, topic + "_prefix");
    connectConfig.put(DATABASE_HOSTNAME, entityConfig.get("databaseUrl"));
    connectConfig.put(DATABASE_PORT, entityConfig.get("databasePort"));
    connectConfig.put(DATABASE_USER, entityConfig.get("databaseUsername"));
    connectConfig.put(DATABASE_PASSWORD, entityConfig.get("databasePassword"));
    connectConfig.put(DATABASE_DBNAME, entityConfig.get("databaseName"));
    String tables = getTables(entityConfig.get("tables"), entityConfig.get("databaseName"));
    connectConfig.put(TABLE_INCLUDE_LIST, tables);
    connectConfig.put(SNAPSHOT_NEW_TABLE, "parallel");
    connectConfig.put(MAX_BATCH_SIZE, 10000);
    connectConfig.put(MAX_QUEUE_SIZE, 15000);
    connectConfig.put(DATABASE_SERVER_NAME, topic);
    connectConfig.put(INCLUDE_SCHEMA_CHANGES, "true");
    connectConfig.put(SCHEMA_HISTORY_INTERNAL_KAFKA_BOOTSTRAP_SERVERS, kafkaBrokers);
    connectConfig.put(SCHEMA_HISTORY_INTERNAL_STORE_ONLY_CAPTURED_DATABASES_DDL, "false");
    connectConfig.put(
        SCHEMA_HISTORY_INTERNAL_KAFKA_TOPIC, "schema-changes." + entityConfig.get("databaseName"));
    connectConfig.put(TIME_PRECISION_MODE, "connect");
    connectConfig.put(
        TRANSFORMS,
        "Reroute,db_connector_id,db_device_type,db_device_vendor,db_event_id,db_fleet_id,db_log_type,db_pipeline_done,db_source_name,db_tenant_id,db_event_source_id,unwrap");
    connectConfig.put(TRANSFORMS_REROUTE_TYPE, TRANSFORMS_REROUTE_TYPE_VALUE);
    connectConfig.put(TRANSFORMS_REROUTE_REGEX, TRANSFORMS_REROUTE_REGEX_VALUE);
    connectConfig.put(TRANSFORMS_REROUTE_REPLACEMENT, DB_RAW_INPUT_CDC_TOPIC);

    // db_connector_id Header
    connectConfig.put(
        "transforms.db_connector_id.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_connector_id.header", "db_connector_id");
    connectConfig.put("transforms.db_connector_id.value.literal", "db_cdc_connector");

    // db_device_type Header
    connectConfig.put(
        "transforms.db_device_type.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_device_type.header", "db_device_type");
    connectConfig.put("transforms.db_device_type.value.literal", "cdc");

    // db_device_vendor Header
    connectConfig.put(
        "transforms.db_device_vendor.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_device_vendor.header", "db_device_vendor");
    connectConfig.put("transforms.db_device_vendor.value.literal", "saviynt");

    // db_event_id Header
    connectConfig.put(
        "transforms.db_event_id.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_event_id.header", "db_event_id");
    connectConfig.put("transforms.db_event_id.value.literal", UUID.randomUUID());

    // db_fleet_id Header
    connectConfig.put(
        "transforms.db_fleet_id.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_fleet_id.header", "db_fleet_id");
    connectConfig.put("transforms.db_fleet_id.value.literal", "");

    // db_log_type Header
    connectConfig.put(
        "transforms.db_log_type.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_log_type.header", "db_log_type");
    connectConfig.put("transforms.db_log_type.value.literal", "json");

    // db_pipeline_done Header
    connectConfig.put(
        "transforms.db_pipeline_done.type", "org.apache.kafka.connect.transforms.InsertHeader");
    connectConfig.put("transforms.db_pipeline_done.header", "db_pipeline_done");
    connectConfig.put("transforms.db_pipeline_done.value.literal", "CC");

    // db and table Header
    connectConfig.put("transforms.unwrap.type", "io.debezium.transforms.ExtractNewRecordState");
    connectConfig.put("transforms.unwrap.add.fields.prefix", "");
    connectConfig.put("transforms.unwrap.drop.tombstones", "true");
    connectConfig.put("transforms.unwrap.delete.handling.mode", "rewrite");
    connectConfig.put("transforms.unwrap.add.headers", "db,table,op,ts_ms:db_edge_ts");
    connectConfig.put("transforms.unwrap.add.headers.prefix", "");
    connectConfig.put("transforms.unwrap.add.fields", "op,ts_ms,source.db:db,source.table:table");

    // TO AVOID OFFSET RE-SETTING
    connectConfig.put("offset.flush.interval.ms", "60000");
    connectConfig.put("offset.flush.timeout.ms", "6000");
    connectConfig.put("poll.interval.ms", "700");
    connectConfig.put("snapshot.mode", "initial");
    // 15MB
    connectConfig.put("producer.override.max.request.size", "15728640");

    return connectConfig;
  }

  private long generate8DigitHash(String str) {
    long hash = 1125899906842597L; // Prime number seed
    int len = str.length();

    for (int i = 0; i < len; i++) {
      hash = 31 * hash + str.charAt(i); // Similar to String.hashCode(); but for long
    }

    // Ensure the result is within 8 digits
    long result = Math.abs(hash) % 100000000L;

    // If the result is less than 8 digits, pad it to ensure it has 8 digits
    while (result < 10000000L) {
      result *= 10;
    }

    return result;
  }

  private String getTables(String tables, String databaseName) {
    String[] parts = tables.split(",");
    for (int i = 0; i < parts.length; i++) {
      parts[i] = databaseName + "." + parts[i];
    }
    return String.join(",", parts);
  }
}
