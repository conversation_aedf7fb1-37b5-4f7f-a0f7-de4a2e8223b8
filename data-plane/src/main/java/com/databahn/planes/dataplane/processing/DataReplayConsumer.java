package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.model.replay.ReplayStatus;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.net.URI;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DataReplayConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  //  FileName:    val.FileName,
  //  RequestId:   val.RequestId,
  //  Status:      val.Status,
  //  FileSize:    val.FileSize,
  //  CurrentSize: val.CurrentSize,
  //  ErrorMsg:    val.ErrorMsg,
  public DataReplayConsumer(ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @Value("${urls.databahn_app:}")
  private String backEndUrl;

  @KafkaListener(
      id = "data_plane_data_replay_consumer1",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.replay.status"},
      concurrency = "${consumer.replay.status.threads:1}")
  public void onReplayStatus(List<ConsumerRecord<String, String>> records) throws Exception {
    log.debug("data_plane_data_replay_consumer {} status", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    List<ReplayStatus> statusList = Lists.newArrayList();

    for (ConsumerRecord<String, String> record : records) {

      try {
        if (record.value() == null || record.value().isEmpty()) {
          log.warn("Empty or Null value :- {}");
          continue;
        }
        String value = record.value();
        List<ReplayStatus> replayStatus = objectMapper.readValue(value, List.class);
        statusList.addAll(replayStatus);
        log.info("value :- {}", value);
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
    // String token = "Bearer
    // eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJMUnpXcXhObEFGQUQzeTd0Z1c3b2N5akx1MTBiYWJaU3JCeVJ6TXJrSHZVIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FKB_9niJecceciVML6H7jax3E514b-cHCqmk-N59g9vU0o8EKjf59Iqq4BpbkuPgxA-UxVK6_mDtgGCBezbn5zDD32BpcvDbTgJITAq1zahNEzUqXfUFLydSlHq-bBhl-XHRZkZDspKYTm47X47qCNWjUzKQaYn74kA0y6tKrjCTvArXSkFT-atuVXNVH-Jm-QitZ-BGwirO6GKOw5BLOsgwN5yPMVbNU7Nb8V6bZepp3UY72lokPfLoGa7847ROwX3-i-HOsZfAY1HIveAackUNtOgmsQ1y5sLk0FNxUg_KCjn1vl0E9RkF84Jc780oqCNoDItAndFy2NfB6Kb2tA";

    try {
      if (!statusList.isEmpty()) {
        if (!backEndUrl.contains("https://")) {
          backEndUrl = "https://" + backEndUrl;
        }
        URI url = URI.create(backEndUrl);
        Response<Object> response = planeClient.sendUpdates(url, statusList);
        if (response.getStatus() == Response.Status.ERROR) {
          log.error("failed to send Replay Status to control plane: {}", response.getError());
          throw new BatchListenerFailedException(
              "error response from control plane " + response.getError(), batchFirstRecord);
        } else {
          log.debug("{} Replay Status saved", statusList.size());
          //  acknowledgment.acknowledge();
          log.debug("size-{} Replay Status acknowledgment sent", statusList.size());
        }
      }
    } catch (Exception e) {
      log.error("failed to send Replay Status to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send Replay Status to control plane", e, batchFirstRecord);
    }
  }
}
