package com.databahn.planes.dataplane.processing;

import com.databahn.planes.exception.ValidationException;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.model.ack.AckTypes;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AckProducer {

  public static final String ACK_TOPIC = "db.management.ack";

  private final ObjectMapper objectMapper;
  private final KafkaTemplate<String, String> kafkaTemplate;

  public AckProducer(
      ObjectMapper objectMapper,
      @Qualifier("inputKafkaTemplate") KafkaTemplate<String, String> kafkaTemplate) {
    this.objectMapper = objectMapper;
    this.kafkaTemplate = kafkaTemplate;
  }

  public void sendToKafkaSync(Ack ack) {
    try {
      String value = objectMapper.writeValueAsString(ack);
      ProducerRecord<String, String> message = new ProducerRecord<>(ACK_TOPIC, value);
      CompletableFuture<SendResult<String, String>> result = kafkaTemplate.send(message);
      result.get();
    } catch (InterruptedException | ExecutionException e) {
      log.error("failed to send to kafka", e);
      throw new RuntimeException(e);
    } catch (JsonProcessingException e) {
      log.error("failed to parse ack " + ack.getId(), e);
      throw new ValidationException("Invalid ack entity", e);
    }
  }

  public Ack buildAck(ChangeFlag cf, String status, String service, String error) {
    Ack ack = new Ack();
    ack.setRequestId(cf.requestId());
    ack.setCustomerId(cf.tenantId());
    ack.setType(AckTypes.CHANGE_FLAG);
    ack.setEntityId(cf.entityId());
    ack.setEntityType(cf.type());
    ack.setEntityVersion(cf.schemaVersion());
    ack.setServiceName(service);
    ack.setTenantId(cf.tenantId());
    ack.setAction(cf.action());
    ack.setStatus(status);
    if (error != null) {
      error = error.length() > 250 ? error.substring(0, 250) : error;
      ack.setError(error);
    }
    return ack;
  }
}
