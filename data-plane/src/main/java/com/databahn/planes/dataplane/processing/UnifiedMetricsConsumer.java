package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.dataplane.config.DataPlaneConfig;
import com.databahn.planes.exception.NoTenantsFoundException;
import com.databahn.planes.model.dataplane.TenantDataPlane;
import com.databahn.planes.response.Response;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnifiedMetricsConsumer {

  private static final String NODE_URL_PREFIX = "node_url | ";
  private static final String KUBERNETES_PREFIX = "k8s_source | ";
  private static final String REDIS_PREFIX = "redis_source | ";

  private final ControlPlaneClient planeClient;
  private final DataPlaneConfig dataPlaneConfig;

  @KafkaListener(
      id = "data_plane_kafka_metrics_consumer",
      topics = "kafka-metrics",
      containerFactory = "processingKafkaListenerContainerFactory",
      batch = "true",
      concurrency = "${consumer.kafka.threads:1}")
  public void onKafkaMetrics(List<ConsumerRecord<String, String>> records) {
    processBatch(records, "kafka");
  }

  @KafkaListener(
      id = "data_plane_kubernetes_metrics_consumer",
      topics = "kubernetes-metrics",
      containerFactory = "processingKafkaListenerContainerFactory",
      batch = "true",
      concurrency = "${consumer.kubernetes.threads:1}")
  public void onKubernetesMetrics(List<ConsumerRecord<String, String>> records) {
    processBatch(records, "kubernetes");
  }

  @KafkaListener(
      id = "data_plane_node_metrics_consumer",
      topics = "node-metrics",
      containerFactory = "processingKafkaListenerContainerFactory",
      batch = "true",
      concurrency = "${consumer.node.threads:1}")
  public void onNodeMetrics(List<ConsumerRecord<String, String>> records) {
    processBatch(records, "node");
  }

  @KafkaListener(
      id = "data_plane_redis_metrics_consumer",
      topics = "redis-metrics",
      containerFactory = "processingKafkaListenerContainerFactory",
      batch = "true",
      concurrency = "${consumer.redis.threads:1}")
  public void onRedisMetrics(List<ConsumerRecord<String, String>> records) {
    processBatch(records, "redis");
  }

  private void processBatch(List<ConsumerRecord<String, String>> records, String metricType) {
    log.info("Processing batch of {} {} metrics", records.size(), metricType);
    ConsumerRecord<String, String> firstRecord = records.get(0);

    try {
      for (ConsumerRecord<String, String> record : records) {
        try {
          // Get the metric value and normalize it if needed
          String metricValue = normalizeMetricFormat(record.value(), metricType);

          Response<Object> response = planeClient.saveMetrics(metricType, metricValue);

          if (response.getStatus() == Response.Status.ERROR) {
            throw new BatchListenerFailedException(
                "Failed to process metric: " + response.getError(), record);
          }
        } catch (NoTenantsFoundException e) {
          log.error(
              "No tenants found for this data plane when processing {} metrics", metricType, e);
          throw new BatchListenerFailedException(
              "No tenants found for this data plane. Cannot process metrics without tenant information.",
              e,
              record);
        } catch (Exception e) {
          log.error(
              "Failed to process {} metric (Topic: {}, Partition: {}, Offset: {})",
              metricType,
              record.topic(),
              record.partition(),
              record.offset(),
              e);
          throw new BatchListenerFailedException(
              "Failed to process " + metricType + " metric", e, record);
        }
      }
      log.info("Successfully processed {} {} metrics", records.size(), metricType);
    } catch (Exception e) {
      log.error("Batch processing failed for {} metrics", metricType, e);
      throw new BatchListenerFailedException(
          "Batch processing failed for " + metricType, e, firstRecord);
    }
  }

  /**
   * Normalizes the metric format to ensure consistency across different metric types. This method
   * handles various format normalizations based on the metric type. It also adds tenant_id label to
   * each metric for tenant-based filtering.
   *
   * @param metricValue The raw metric value to normalize
   * @param metricType The type of metric (kafka, kubernetes, node, redis, etc.)
   * @return The normalized metric value in standard Prometheus format with tenant_id label
   */
  private String normalizeMetricFormat(String metricValue, String metricType) {
    if (metricValue == null || metricValue.isEmpty()) {
      return metricValue;
    }

    // First normalize the metric format based on type
    String normalizedMetric;

    switch (metricType) {
      case "kafka":
        // Handle Kafka metrics - remove prefixes like "broker-10001 | "
        int pipeIndex = metricValue.indexOf(" | ");
        if (pipeIndex > 0) {
          normalizedMetric = metricValue.substring(pipeIndex + 3);
        } else {
          normalizedMetric = metricValue;
        }
        break;

      case "kubernetes":
        // Handle Kubernetes metrics - remove prefixes like "k8s_source | "
        if (metricValue.startsWith(KUBERNETES_PREFIX)) {
          normalizedMetric = metricValue.substring(KUBERNETES_PREFIX.length());
        } else {
          normalizedMetric = metricValue;
        }
        break;

      case "node":
        // Handle Node metrics - remove prefixes like "node_url | "
        if (metricValue.startsWith(NODE_URL_PREFIX)) {
          normalizedMetric = metricValue.substring(NODE_URL_PREFIX.length());
        } else {
          normalizedMetric = metricValue;
        }
        break;

      case "redis":
        // Handle Redis metrics - remove prefixes like "redis_source | "
        if (metricValue.startsWith(REDIS_PREFIX)) {
          normalizedMetric = metricValue.substring(REDIS_PREFIX.length());
        } else {
          normalizedMetric = metricValue;
        }
        break;

      default:
        // For any other metric types, look for a generic pattern like "source | "
        int genericPipeIndex = metricValue.indexOf(" | ");
        if (genericPipeIndex > 0) {
          normalizedMetric = metricValue.substring(genericPipeIndex + 3);
        } else {
          normalizedMetric = metricValue;
        }
        break;
    }

    // Now add tenant_id labels to the metric for all tenants associated with this data plane
    return addTenantIdLabels(normalizedMetric);
  }

  /**
   * Adds tenant_id labels to a Prometheus format metric for all tenants associated with this data
   * plane. This ensures metrics can be filtered by tenant_id for access control in a multi-tenant
   * environment. In a true multi-tenant environment, we duplicate the metric for each tenant to
   * ensure proper isolation.
   *
   * @param metric The metric in Prometheus format
   * @return The metrics with tenant_id labels added, one for each tenant
   */
  private String addTenantIdLabels(String metric) {
    if (metric == null || metric.isEmpty()) {
      return metric;
    }

    // Get the tenant IDs associated with this data plane
    List<TenantDataPlane> tenantDataPlanes = dataPlaneConfig.getTenantDataPlanes();
    if (tenantDataPlanes.isEmpty()) {
      log.error("No tenants found for this data plane when processing metrics.");
      throw new NoTenantsFoundException(
          "No tenants found for this data plane. Cannot process metrics without tenant information.");
    }

    // In a multi-tenant environment, we need to create a separate metric for each tenant
    // This ensures proper isolation between tenants
    StringBuilder result = new StringBuilder();
    for (TenantDataPlane tenantDataPlane : tenantDataPlanes) {
      UUID tenantId = tenantDataPlane.getTenantId();
      String metricWithTenant = addTenantIdToMetric(metric, tenantId);
      result.append(metricWithTenant).append("\n");
    }

    return result.toString().trim();
  }

  /**
   * Adds tenant_id and data_plane_id labels to a Prometheus format metric.
   *
   * @param metric The metric in Prometheus format
   * @param tenantId The tenant ID to add as a label
   * @return The metric with tenant_id and data_plane_id labels added
   */
  private String addTenantIdToMetric(String metric, UUID tenantId) {
    // Prometheus metrics follow the format: metric_name{label1="value1",label2="value2"} value
    // We need to add tenant_id and data_plane_id labels to the existing labels or create labels if
    // none exist

    // Pattern to match Prometheus metric format
    Pattern pattern = Pattern.compile("^([\\w_:]+)(\\{([^}]*)\\})?\\s+(.+)$");
    Matcher matcher = pattern.matcher(metric);

    if (matcher.find()) {
      String metricName = matcher.group(1);
      String existingLabels = matcher.group(3);
      String value = matcher.group(4);
      UUID dataPlaneId = dataPlaneConfig.getDataPlaneId();

      // Add tenant_id and data_plane_id labels
      if (existingLabels != null && !existingLabels.isEmpty()) {
        // Add tenant_id and data_plane_id to existing labels
        return String.format(
            "%s{%s,tenant_id=\"%s\",data_plane_id=\"%s\"} %s",
            metricName, existingLabels, tenantId, dataPlaneId, value);
      } else {
        // Create new labels with tenant_id and data_plane_id
        return String.format(
            "%s{tenant_id=\"%s\",data_plane_id=\"%s\"} %s",
            metricName, tenantId, dataPlaneId, value);
      }
    }

    // If the metric doesn't match the expected format, return it unchanged
    log.warn("Metric doesn't match expected Prometheus format: {}", metric);
    return metric;
  }
}
