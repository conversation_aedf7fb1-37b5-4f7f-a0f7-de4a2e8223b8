package com.databahn.planes.dataplane.processing;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OpenTelemetryStatisticsConsumer {

  private final ControlPlaneClient planeClient;
  private final ObjectMapper objectMapper;

  public OpenTelemetryStatisticsConsumer(
      ControlPlaneClient planeClient, ObjectMapper objectMapper) {
    this.planeClient = planeClient;
    this.objectMapper = objectMapper;
  }

  @KafkaListener(
      id = "data_plane_open_telemetry_statistics_consumer",
      containerFactory = "kafkaListenerContainerFactory",
      batch = "true",
      topics = {"db.metrics.otel"},
      concurrency = "${consumer.statistics.open_telemetry.threads:1}")
  public void onStatistics(List<ConsumerRecord<String, String>> records) {
    log.debug(
        "data_plane_open_telemetry_statistics_consumer received {} statistics", records.size());
    ConsumerRecord<String, String> batchFirstRecord = records.get(0);
    List<JsonNode> stats = Lists.newArrayList();
    for (ConsumerRecord<String, String> record : records) {
      String value = record.value();
      try {
        JsonNode jsonNode = objectMapper.readTree(value);
        stats.add(jsonNode);
      } catch (JsonProcessingException e) {
        log.error("failed to parse open telemetry statistics:" + value, e);
        throw new BatchListenerFailedException(
            "failed to parse open telemetry statistics", e, batchFirstRecord);
      }
    }
    try {
      Response<Object> response = planeClient.saveOpenTelemetryStatistics(stats);
      if (response.getStatus() == Response.Status.ERROR) {
        log.error(
            "failed to send open telemetry statistics to control plane: {}", response.getError());
        throw new BatchListenerFailedException(
            "error response from control plane " + response.getError(), batchFirstRecord);
      } else if (response.getStatus() == Response.Status.PARTIAL_FAILURE) {
        log.error(
            "send open telemetry statistics to control plane partially failed: {}",
            response.getData());
      } else {
        log.debug("{} open telemetry statistics saved", stats.size());
      }
    } catch (Exception e) {
      log.error("failed to send open telemetry statistics to control plane", e);
      throw new BatchListenerFailedException(
          "failed to send open telemetry statistics to control plane", e, batchFirstRecord);
    }
  }
}
