package com.databahn.planes.dataplane;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.databahn.planes"})
@EnableFeignClients(basePackages = "com.databahn.planes.dataplane.client")
@EnableScheduling
public class DataPlaneControllerApplication {
  public static void main(String[] args) {
    SpringApplication.run(DataPlaneControllerApplication.class, args);
  }
}
