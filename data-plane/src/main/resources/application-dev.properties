# Local development configuration
spring.config.import=optional:file:/opt/databahn/config/app21.yaml

# Kafka configuration for local development
kafka.input.bootstrap_brokers=localhost:9092
kafka.processing.bootstrap_brokers=localhost:9092

# Data plane ID for local development
dataplane.id=00000000-0000-0000-0000-000000000000

# OAuth configuration for local development
oauth.backend_client_id=local-dev-client
oauth.backend_client_secret=local-dev-secret

# URLs configuration
urls.authentication_url=localhost
urls.databahn_app=localhost:3000

# Disable security for local development
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost/dummy
