package com.databahn.planes.dataplane.processing;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.when;

import com.databahn.planes.dataplane.client.ControlPlaneClient;
import com.databahn.planes.dataplane.config.DataPlaneConfig;
import com.databahn.planes.exception.NoTenantsFoundException;
import com.databahn.planes.model.dataplane.TenantDataPlane;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UnifiedMetricsConsumerTest {

  @Mock private ControlPlaneClient planeClient;
  @Mock private DataPlaneConfig dataPlaneConfig;

  @InjectMocks private UnifiedMetricsConsumer unifiedMetricsConsumer;

  private UUID testTenantId;
  private UUID testDataPlaneId;

  @BeforeEach
  void setUp() {
    testTenantId = UUID.fromString("11111111-1111-1111-1111-111111111111");
    testDataPlaneId = UUID.fromString("*************-2222-2222-************");
  }

  @Test
  void testAddTenantIdToMetricWithExistingLabels() throws Exception {
    // Use reflection to access private method
    Method method =
        UnifiedMetricsConsumer.class.getDeclaredMethod(
            "addTenantIdToMetric", String.class, UUID.class);
    method.setAccessible(true);

    // Mock the data plane ID
    when(dataPlaneConfig.getDataPlaneId()).thenReturn(testDataPlaneId);

    String metric =
        "org_apache_kafka_server_assignmentsmanager_value{broker_id=\"10001\",name=\"QueuedReplicaToDirAssignments\"} 0.0";
    String expected =
        "org_apache_kafka_server_assignmentsmanager_value{broker_id=\"10001\",name=\"QueuedReplicaToDirAssignments\",tenant_id=\"11111111-1111-1111-1111-111111111111\",data_plane_id=\"*************-2222-2222-************\"} 0.0";

    String result = (String) method.invoke(unifiedMetricsConsumer, metric, testTenantId);
    assertEquals(expected, result);
  }

  @Test
  void testAddTenantIdToMetricWithoutLabels() throws Exception {
    // Use reflection to access private method
    Method method =
        UnifiedMetricsConsumer.class.getDeclaredMethod(
            "addTenantIdToMetric", String.class, UUID.class);
    method.setAccessible(true);

    // Mock the data plane ID
    when(dataPlaneConfig.getDataPlaneId()).thenReturn(testDataPlaneId);

    String metric = "node_cpu_seconds_total 1234.5";
    String expected =
        "node_cpu_seconds_total{tenant_id=\"11111111-1111-1111-1111-111111111111\",data_plane_id=\"*************-2222-2222-************\"} 1234.5";

    String result = (String) method.invoke(unifiedMetricsConsumer, metric, testTenantId);
    assertEquals(expected, result);
  }

  @Test
  void testAddTenantIdLabels() throws Exception {
    // Use reflection to access private method
    Method method =
        UnifiedMetricsConsumer.class.getDeclaredMethod("addTenantIdLabels", String.class);
    method.setAccessible(true);

    // Mock the tenant data planes with a single tenant
    List<TenantDataPlane> tenantDataPlanes = new ArrayList<>();
    tenantDataPlanes.add(new TenantDataPlane(testTenantId, testDataPlaneId));
    when(dataPlaneConfig.getTenantDataPlanes()).thenReturn(tenantDataPlanes);
    when(dataPlaneConfig.getDataPlaneId()).thenReturn(testDataPlaneId);

    String metric = "node_cpu_seconds_total 1234.5";
    String result = (String) method.invoke(unifiedMetricsConsumer, metric);

    assertTrue(result.contains("tenant_id=\"11111111-1111-1111-1111-111111111111\""));
    assertTrue(result.contains("data_plane_id=\"*************-2222-2222-************\""));
  }

  @Test
  void testNormalizeMetricFormat() throws Exception {
    // Use reflection to access private method
    Method method =
        UnifiedMetricsConsumer.class.getDeclaredMethod(
            "normalizeMetricFormat", String.class, String.class);
    method.setAccessible(true);

    // Mock the tenant data planes
    List<TenantDataPlane> tenantDataPlanes = new ArrayList<>();
    tenantDataPlanes.add(new TenantDataPlane(testTenantId, testDataPlaneId));
    when(dataPlaneConfig.getTenantDataPlanes()).thenReturn(tenantDataPlanes);
    when(dataPlaneConfig.getDataPlaneId()).thenReturn(testDataPlaneId);

    // Test Kafka metric normalization
    String kafkaMetric =
        "broker-10001 | org_apache_kafka_server_assignmentsmanager_value{name=\"QueuedReplicaToDirAssignments\"} 0.0";
    String result = (String) method.invoke(unifiedMetricsConsumer, kafkaMetric, "kafka");
    assertTrue(result.contains("tenant_id=\"11111111-1111-1111-1111-111111111111\""));
    assertTrue(result.contains("data_plane_id=\"*************-2222-2222-************\""));
    assertTrue(!result.contains("broker-10001 | "));

    // Test Kubernetes metric normalization
    String k8sMetric =
        "k8s_source | kube_pod_container_status_ready{container=\"kube-apiserver\",namespace=\"kube-system\",pod=\"kube-apiserver-master\"} 1";
    result = (String) method.invoke(unifiedMetricsConsumer, k8sMetric, "kubernetes");
    assertTrue(result.contains("tenant_id=\"11111111-1111-1111-1111-111111111111\""));
    assertTrue(result.contains("data_plane_id=\"*************-2222-2222-************\""));
    assertTrue(!result.contains("k8s_source | "));

    // Test Node metric normalization
    String nodeMetric = "node_url | node_cpu_seconds_total{cpu=\"0\",mode=\"idle\"} 1234.5";
    result = (String) method.invoke(unifiedMetricsConsumer, nodeMetric, "node");
    assertTrue(result.contains("tenant_id=\"11111111-1111-1111-1111-111111111111\""));
    assertTrue(result.contains("data_plane_id=\"*************-2222-2222-************\""));
    assertTrue(!result.contains("node_url | "));

    // Test Redis metric normalization
    String redisMetric = "redis_source | redis_connected_clients{instance=\"redis:6379\"} 10";
    result = (String) method.invoke(unifiedMetricsConsumer, redisMetric, "redis");
    assertTrue(result.contains("tenant_id=\"11111111-1111-1111-1111-111111111111\""));
    assertTrue(result.contains("data_plane_id=\"*************-2222-2222-************\""));
    assertTrue(!result.contains("redis_source | "));
  }

  @Test
  void testAddTenantIdLabelsWithMultipleTenants() throws Exception {
    // Use reflection to access private method
    Method method =
        UnifiedMetricsConsumer.class.getDeclaredMethod("addTenantIdLabels", String.class);
    method.setAccessible(true);

    // Mock the tenant data planes with multiple tenants
    List<TenantDataPlane> multiTenants = new ArrayList<>();
    UUID tenant1 = UUID.fromString("11111111-1111-1111-1111-111111111111");
    UUID tenant2 = UUID.fromString("*************-2222-2222-************");
    UUID tenant3 = UUID.fromString("*************-3333-3333-************");
    multiTenants.add(new TenantDataPlane(tenant1, testDataPlaneId));
    multiTenants.add(new TenantDataPlane(tenant2, testDataPlaneId));
    multiTenants.add(new TenantDataPlane(tenant3, testDataPlaneId));
    when(dataPlaneConfig.getTenantDataPlanes()).thenReturn(multiTenants);
    when(dataPlaneConfig.getDataPlaneId()).thenReturn(testDataPlaneId);

    String metric = "node_cpu_seconds_total 1234.5";
    String result = (String) method.invoke(unifiedMetricsConsumer, metric);

    // Verify that the result contains all tenant IDs and the data plane ID
    assertTrue(result.contains("tenant_id=\"11111111-1111-1111-1111-111111111111\""));
    assertTrue(result.contains("tenant_id=\"*************-2222-2222-************\""));
    assertTrue(result.contains("tenant_id=\"*************-3333-3333-************\""));
    assertTrue(result.contains("data_plane_id=\"*************-2222-2222-************\""));

    // Verify that we have multiple lines (one for each tenant)
    String[] lines = result.split("\n");
    assertEquals(3, lines.length);
  }

  @Test
  void testAddTenantIdLabelsWithNoTenants() throws Exception {
    // Use reflection to access private method
    Method method =
        UnifiedMetricsConsumer.class.getDeclaredMethod("addTenantIdLabels", String.class);
    method.setAccessible(true);

    // Mock empty tenant data planes
    List<TenantDataPlane> emptyTenants = new ArrayList<>();
    when(dataPlaneConfig.getTenantDataPlanes()).thenReturn(emptyTenants);

    String metric = "node_cpu_seconds_total 1234.5";

    // Verify that NoTenantsFoundException is thrown when no tenants are found
    try {
      method.invoke(unifiedMetricsConsumer, metric);
      fail("Expected NoTenantsFoundException to be thrown");
    } catch (InvocationTargetException e) {
      assertTrue(e.getCause() instanceof NoTenantsFoundException);
      assertTrue(e.getCause().getMessage().contains("No tenants found for this data plane"));
    }
  }
}
