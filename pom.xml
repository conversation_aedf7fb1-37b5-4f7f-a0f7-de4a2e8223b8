<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wilddiary</groupId>
    <artifactId>spring-base-starter</artifactId>
    <version>1.1.1</version>
    <relativePath/>
  </parent>

  <groupId>com.databahn.plane</groupId>
  <artifactId>plane-controllers</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modules>
    <module>common</module>
    <module>data-plane</module>
    <module>control-plane</module>
  </modules>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <dependency-check.skip>true</dependency-check.skip>
    <checkstyle.skip>true</checkstyle.skip>
    <jacoco.skip>true</jacoco.skip>
    <jacoco-min-coverage.ratio>0</jacoco-min-coverage.ratio>
    <jacoco-missed-classes.count>10</jacoco-missed-classes.count>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <maven-compiler-plugin.lombok.version>1.18.28</maven-compiler-plugin.lombok.version>
    <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    <guava.version>32.1.3-jre</guava.version>
    <spring-boot-autoconfigure.version>3.0.7</spring-boot-autoconfigure.version>
    <springdoc-openapi-starter-webmvc-ui.version>2.1.0</springdoc-openapi-starter-webmvc-ui.version>
    <spring-boot-maven-plugin.version>3.1.1</spring-boot-maven-plugin.version>
    <spring-data-opensearch-starter.version>1.2.1</spring-data-opensearch-starter.version>
    <spring-data-redis.version>3.2.0</spring-data-redis.version>
    <micrometer-registry-prometheus.version>1.12.3</micrometer-registry-prometheus.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.databahn.plane</groupId>
        <artifactId>common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>${spring-boot-autoconfigure.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        <version>${springdoc-openapi-starter-webmvc-ui.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>io.awspring.cloud</groupId>
        <artifactId>spring-cloud-starter-aws-secrets-manager-config</artifactId>
        <version>2.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.opensearch.client</groupId>
        <artifactId>spring-data-opensearch-starter</artifactId>
        <version>${spring-data-opensearch-starter.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>${spring-data-redis.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <!-- required by all projects -->
  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
          <configuration>
            <annotationProcessorPaths>
              <path>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
              </path>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${maven-compiler-plugin.lombok.version}</version>
              </path>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
              </path>
            </annotationProcessorPaths>
            <compilerArgs>
              <compilerArg>
                -Amapstruct.defaultComponentModel=spring
              </compilerArg>
            </compilerArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring-boot-maven-plugin.version}</version>
          <configuration>
            <imageName>databahn/${project.artifactId}</imageName>
            <excludes>
              <exclude>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
              </exclude>
            </excludes>
          </configuration>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>