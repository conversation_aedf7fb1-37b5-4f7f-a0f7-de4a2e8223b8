package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.StatsService;
import com.databahn.planes.model.stats.Stats;
import com.databahn.planes.model.stats.VectorStats;
import com.databahn.planes.response.Response;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/stats")
public class StatsController {

  private StatsService statsService;

  @PostMapping()
  public Response<?> postStats(@RequestBody Stats stats) {
    if (stats == null || stats.stats() == null) {
      return Response.Ok("Nothing to save");
    }
    statsService.postStatsToKafka(stats.stats());
    return Response.Message("Stats saved");
  }

  @PostMapping("/open_telemetry")
  public Response<?> postOpenTelStats(@RequestBody List<JsonNode> stats) {
    if (CollectionUtils.isEmpty(stats)) {
      return Response.Ok("Nothing to save");
    }
    statsService.postOpenTelStatsToKafka(stats);
    return Response.Message("Stats saved");
  }

  @PostMapping("/vector")
  public Response<?> postVectorStats(@RequestBody VectorStats stats) {
    if (stats == null || CollectionUtils.isEmpty(stats.stats())) {
      return Response.Ok("Nothing to save");
    }
    statsService.postVectorToKafka(stats);
    return Response.Message("Stats saved");
  }
}
