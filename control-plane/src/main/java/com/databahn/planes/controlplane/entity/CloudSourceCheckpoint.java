package com.databahn.planes.controlplane.entity;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;

@Data
@Entity
@DynamicInsert
@Table(name = "cloud_source_checkpoints")
@IdClass(CloudSourceCheckpointId.class)
public class CloudSourceCheckpoint implements Serializable {
  @Id
  @Column(name = "tenant_id")
  private UUID tenantId;

  @Id
  @Column(name = "source_id")
  private UUID sourceId;

  @Column(name = "data_plane_id")
  private UUID dataPlaneId;

  @Column(name = "checkpoint")
  private String checkpoint;

  @Column(name = "created_at")
  private Instant createdAt;

  @Column(name = "updated_at")
  private Instant updatedAt;
}
