package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.common.InsightsRuleAttributes;
import com.databahn.planes.controlplane.entity.InsightsRule;
import com.databahn.planes.controlplane.entity.Lookup;
import com.databahn.planes.controlplane.repo.InsightsRuleRepo;
import com.databahn.planes.controlplane.repo.LookupRepository;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Service;

@Service
public class InsightsRuleService {

  private InsightsRuleRepo insightsRuleRepo;
  private LookupRepository lookupRepository;
  private Map<UUID, InsightsRuleAttributes> insightsRuleAttributesCache;
  private Map<UUID, Lookup> dynamicLookupIdToLookup;

  public InsightsRuleService(InsightsRuleRepo insightsRuleRepo, LookupRepository lookupRepository) {
    this.insightsRuleRepo = insightsRuleRepo;
    this.lookupRepository = lookupRepository;
    insightsRuleAttributesCache = new ConcurrentHashMap<>();
    dynamicLookupIdToLookup = new ConcurrentHashMap<>();
  }

  public Optional<InsightsRuleAttributes> getAttributes(UUID ruleId) {
    if (insightsRuleAttributesCache.containsKey(ruleId)) {
      return Optional.of(insightsRuleAttributesCache.get(ruleId));
    } else {
      Optional<InsightsRule> rule = insightsRuleRepo.findById(ruleId);
      rule.ifPresent(
          insightsRule -> insightsRuleAttributesCache.put(ruleId, insightsRule.getAttributes()));
      return rule.map(InsightsRule::getAttributes);
    }
  }

  public Optional<Lookup> getDynamicLookup(UUID lookupId) {
    if (dynamicLookupIdToLookup.containsKey(lookupId)) {
      return Optional.of(dynamicLookupIdToLookup.get(lookupId));
    } else {
      Optional<Lookup> lookup = lookupRepository.findById(lookupId);
      lookup.ifPresent(id -> dynamicLookupIdToLookup.put(lookupId, lookup.get()));
      return lookup;
    }
  }
}
