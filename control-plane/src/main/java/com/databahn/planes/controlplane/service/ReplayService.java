package com.databahn.planes.controlplane.service;

import static com.databahn.planes.controlplane.constants.ReplayConstants.*;

import com.databahn.planes.constants.ChangeFlagAction;
import com.databahn.planes.constants.EntityType;
import com.databahn.planes.controlplane.constants.ReplayConstants;
import com.databahn.planes.controlplane.entity.DataReplayChangeFlag;
import com.databahn.planes.controlplane.entity.ReplayJob;
import com.databahn.planes.controlplane.entity.ReplayJobExecution;
import com.databahn.planes.controlplane.repo.DataReplayExecutionsRepository;
import com.databahn.planes.controlplane.repo.DataReplayRepository;
import com.databahn.planes.model.replay.Status;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReplayService {

  private final DataReplayRepository dataReplayRepository;
  private final DataReplayExecutionsRepository dataReplayExecutionsRepository;
  private final ObjectMapper objectMapper;

  private final DataplaneService dataplaneService;
  private final ChangeFlagProducer changeFlagProducer;

  public ReplayService(
      DataReplayRepository dataReplayRepository,
      DataReplayExecutionsRepository dataReplayExecutionsRepository,
      ObjectMapper objectMapper,
      DataplaneService dataplaneService,
      ChangeFlagProducer changeFlagProducer) {
    this.dataReplayRepository = dataReplayRepository;
    this.dataReplayExecutionsRepository = dataReplayExecutionsRepository;
    this.objectMapper = objectMapper;
    this.dataplaneService = dataplaneService;
    this.changeFlagProducer = changeFlagProducer;
  }

  public void updateStatus(List<Status> replayStatusDtoList) throws Exception {

    try {
      if (replayStatusDtoList == null || replayStatusDtoList.isEmpty()) {
        log.error("Entity is Empty");
        throw new EntityNotFoundException("Unable to save status for Job Coming from data plane");
      }

      final Map<UUID, UUID> reqIdSet = new HashMap<>();
      List<Status> statusListProcessed = preProcessStatus(replayStatusDtoList);
      for (Status dto : statusListProcessed) {

        log.info(
            "PartId: {}, Status :{} ,File {}",
            dto.getRequestId(),
            dto.getStatus(),
            dto.getFilePath());

        Optional<ReplayJobExecution> replayJobExecution =
            dataReplayExecutionsRepository.findReplayJobExecutionByFileNameAndPartId(
                dto.getFilePath(), UUID.fromString(dto.getRequestId()));
        if (replayJobExecution.isPresent()) {
          ReplayJobExecution job = getReplayJobExecution(dto, replayJobExecution);
          reqIdSet.put(job.getPartId(), job.getJob().getId());
          dataReplayExecutionsRepository.save(replayJobExecution.get());
          log.info(
              "PartId: {}, Status :{} ,File {}",
              dto.getRequestId(),
              dto.getStatus(),
              dto.getFilePath());
        }
      }

      log.info("Status Saved Successfully ");
      process(reqIdSet);
      log.info("Stats calculated Saved Successfully ");

    } catch (Exception e) {
      log.error("Error while saving Status:- {}", e.getMessage());
      e.printStackTrace();
      throw new Exception("Unable to save status for Job from data plane");
    }
  }

  private static ReplayJobExecution getReplayJobExecution(
      Status dto, Optional<ReplayJobExecution> replayJobExecution) {
    ReplayJobExecution job = replayJobExecution.get();
    job.setStatus(dto.getStatus());
    job.setError(dto.getErrorMsg() != null ? dto.getErrorMsg().toString() : "");
    job.setProgress(dto.getPercentage());
    job.setLines(dto.getCurrentSize());
    if (dto.getStartTime() != null) {
      job.setStartTime(Date.from(dto.getStartTime().toInstant()));
    }
    if (dto.getEndTime() != null) {
      job.setEndTime(Date.from(dto.getEndTime().toInstant()));
    }
    return job;
  }

  private List<Status> preProcessStatus(List<Status> statusDtoList) {
    LinkedHashMap<String, Status> replayStatusMap = new LinkedHashMap<>();

    for (Status dto : statusDtoList) {
      if (dto.getRequestId() == null || dto.getFileName() == null || dto.getFileName().isEmpty()) {
        continue;
      }
      replayStatusMap.put(dto.getFileName(), dto);
    }
    return replayStatusMap.values().stream().toList();
  }

  private String process(Map<UUID, UUID> partIdJobIdMap) {

    try {

      if (!partIdJobIdMap.isEmpty()) {

        for (UUID partId : partIdJobIdMap.keySet()) {

          List<UUID> jobIdList = dataReplayExecutionsRepository.findDistinctJobIdByPartId(partId);
          List<Object[]> statusAndCountByJobId =
              dataReplayExecutionsRepository.getStatusAndCountByJobId(jobIdList.get(0));

          long countOfSize = 0;
          try {
            Optional<Object[]> data =
                dataReplayExecutionsRepository.getSumOfDataByJobId(jobIdList.get(0));
            countOfSize = Integer.parseInt(data.get()[0].toString());
          } catch (Exception e) {
            log.error("unable to calculate sum of data  ", e.getMessage());
          }

          int failed = 0;
          int success = 0;
          int partial = 0;
          int processing = 0;
          int totalFiles = 0;
          for (Object[] statusAndCounts : statusAndCountByJobId) {

            int count = Integer.parseInt(statusAndCounts[1].toString());
            totalFiles = totalFiles + count;

            switch (statusAndCounts[0].toString()) {
              case STATUS_COMPLETED:
                success = success + count;
                break;
              case STATUS_FAILED:
                failed = failed + count;
                break;
              case STATUS_COMPLETED_WITH_ERROR:
              case STATUS_DOWNLOAD_FAILED:
                partial = partial + count;
                break;
              case STATUS_DOWNLOAD:
              case STATUS_IN_PROGRESS:
              case STATUS_YET_TO_PROCESS:
              case STATUS_PROCESSING:
              case STATUS_PENDING:
                processing = processing + count;
                break;
            }
          }

          log.info(
              "JobId - {} ,Success - {}, failed - {}, Partial - {} ,Processing - {} , total - {} ",
              partIdJobIdMap.get(partId).toString(),
              success,
              failed,
              partial,
              processing,
              totalFiles);
          String status = "";
          boolean sendDeleteFlag = false;

          if (processing > 0) {
            status = STATUS_IN_PROGRESS;
          } else if ((failed == (totalFiles)) || (success == 0 && failed == 0 && partial > 0)) {
            status = STATUS_FAILED;
            sendDeleteFlag = true;
          } else if (success == totalFiles) {
            status = STATUS_COMPLETED;
            sendDeleteFlag = true;
          } else if (success > 0 && (failed > 0 || partial > 0)) {
            status = STATUS_COMPLETED_WITH_ERROR;
          }
          Optional<ReplayJob> job = dataReplayRepository.findById(partIdJobIdMap.get(partId));
          job.get().setStatus(status);
          float progress = ((float) success / totalFiles) * 100;

          job.get().setProgress(progress);
          Map<String, Object> statsMap = new HashMap<>();
          statsMap.put(TOTAL, totalFiles);
          statsMap.put(STATUS_COMPLETED, success);
          statsMap.put(STATUS_COMPLETED_WITH_ERROR, partial);
          statsMap.put(STATUS_FAILED, failed);
          statsMap.put(STATUS_IN_PROGRESS, processing);
          statsMap.put(TOTAL_FILE_SIZE, countOfSize);
          try {
            job.get().setStats(objectMapper.writeValueAsString(statsMap));
          } catch (JsonProcessingException e) {
            log.info(
                "Unable to calculate stats For JobId - {} ,Success - {}, failed - {}, Partial - {} ,Processing - {} , total - {} , error {} ",
                partIdJobIdMap.get(partId).toString(),
                success,
                failed,
                partial,
                processing,
                totalFiles,
                e.getMessage());
          }
          dataReplayRepository.save(job.get());
          if (sendDeleteFlag) {

            try {
              sendControlFlag(partId, JOB_DELETE, job.get(), "delete_files");

            } catch (Exception e) {
              log.error("failed to send change flags to data plane", e);
              throw new Exception("failed to send change flags to data plane", e);
            }
          }
        }
      }
    } catch (Exception e) {
      log.error("Unable to process  status {} ", e.getMessage());
      e.printStackTrace();
    }
    return null;
  }

  public void sendControlFlag(UUID partId, String operation, ReplayJob replayJob, String fileNames)
      throws JsonProcessingException {

    DataReplayChangeFlag dataReplayChangeFlag =
        buildChangeFlag(partId, operation, replayJob, fileNames);
    changeFlagProducer.sendChangeFlag(
        replayJob.getTenantId().toString(),
        replayJob.getDataPlane().getId().toString(),
        EntityType.DATA_REPLAY,
        ChangeFlagAction.DELETE,
        partId.toString(),
        dataReplayChangeFlag);
    log.debug("Sending Change Flag for Data Replay");
  }

  private DataReplayChangeFlag buildChangeFlag(
      UUID partId, String operation, ReplayJob replayJob, String fileNames)
      throws JsonProcessingException {
    DataReplayChangeFlag replayChangeFlag = new DataReplayChangeFlag();
    replayChangeFlag.setId(partId.toString());
    replayChangeFlag.setAction(operation);
    replayChangeFlag.setRequestId(partId.toString());
    Map<String, String> configs = new ObjectMapper().readValue(replayJob.getConfig(), Map.class);
    replayChangeFlag.setDestination(replayJob.getSourceId().toString());
    replayChangeFlag.setSource(replayJob.getSourceId().toString());

    replayChangeFlag.setBucketName(configs.get(ReplayConstants.BUCKET));
    replayChangeFlag.setBucketPrefix(configs.get(ReplayConstants.PATH));
    replayChangeFlag.setAccessKeyId(configs.get(ReplayConstants.ACCESS_KEY_ID));
    replayChangeFlag.setSecretAccessKey(configs.get(ReplayConstants.SECRET_ACCESS_KEY));
    replayChangeFlag.setRegion(configs.get(REGION));
    replayChangeFlag.setFileName(fileNames);
    replayChangeFlag.setDeviceType(replayJob.getDeviceType());
    replayChangeFlag.setDeviceVendor(replayJob.getDeviceVendor());
    replayChangeFlag.setLogType(replayJob.getLogType());
    replayChangeFlag.setFleetId(
        replayJob.getFleetId() != null ? replayJob.getFleetId().toString() : null);
    replayChangeFlag.setConnectorId(
        replayJob.getConnectId() != null ? replayJob.getConnectId().toString() : null);
    replayChangeFlag.setTenantId(replayJob.getTenantId().toString());
    return replayChangeFlag;
  }
}
