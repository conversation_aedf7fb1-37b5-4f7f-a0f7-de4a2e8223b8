package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.LookupService;
import com.databahn.planes.model.lookup.*;
import com.databahn.planes.response.Response;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/lookup")
public class LookupController {

  private LookupService lookupService;

  @PostMapping("/values")
  public Response<?> saveLookupValues(@RequestBody LookupValues values) {
    if (values == null || values.getLookupValues() == null) {
      return Response.Ok("Nothing to save");
    }
    lookupService.saveLookupValues(values.getLookupValues());
    return Response.Message("Lookup values saved");
  }

  @GetMapping("/requests/cache/pending")
  public Response<List<LookupCacheRequest>> getPendingLookupCacheRequests(
      @RequestHeader("tenantId") UUID tenantId, @RequestHeader("dataPlaneId") UUID dataPlaneId) {
    List<LookupCacheRequest> requests =
        lookupService.getPendingLookupCacheRequests(tenantId, dataPlaneId);
    return Response.Ok(requests);
  }

  @PostMapping("/requests/cache/operations")
  public Response<CacheOperationDetails> getLookupCache(
      @RequestBody CacheOperationRequest request) {
    CacheOperationDetails cacheOperation =
        lookupService.getLookupDataToCache(
            request.getRequestId(),
            request.getLookupOperations(),
            request.getSize(),
            request.getSearchAfter());
    return Response.Ok(cacheOperation);
  }

  @PutMapping("/requests/cache/status")
  public Response<String> updateLookupCacheRequest(@RequestBody CacheOperationStatus status) {
    lookupService.updateLookupCacheRequestStatus(
        status.getRequestId(),
        status.getTenantId(),
        status.getDataPlaneId(),
        status.getLookupOperations(),
        status.getStatus(),
        status.getError());
    return Response.Ok("Lookup cache request updated");
  }

  @GetMapping("/requests/dynamic/cache/pending")
  public Response<List<DynamicLookupCacheRequest>> getPendingDynamicLookupCacheRequests(
      @RequestHeader("tenantId") UUID tenantId, @RequestHeader("dataPlaneId") UUID dataPlaneId) {
    List<DynamicLookupCacheRequest> requests =
        lookupService.getPendingDynamicLookupCacheRequests(tenantId, dataPlaneId);
    return Response.Ok(requests);
  }

  @PostMapping("/requests/dynamic/cache/operations")
  public Response<DynamicCacheOperationDetails> getDynamicLookupCache(
      @RequestBody DynamicCacheOperationRequest request) {
    DynamicCacheOperationDetails cacheOperation =
        lookupService.getDynamicLookupDataToCache(request);
    return Response.Ok(cacheOperation);
  }
}
