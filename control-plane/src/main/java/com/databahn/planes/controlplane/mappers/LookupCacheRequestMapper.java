package com.databahn.planes.controlplane.mappers;

import com.databahn.planes.controlplane.model.LookupCacheRequestWithIndex;
import com.databahn.planes.model.lookup.LookupCacheRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    componentModel = "spring")
public interface LookupCacheRequestMapper {
  LookupCacheRequestMapper INSTANCE = Mappers.getMapper(LookupCacheRequestMapper.class);

  @Mapping(target = "id", source = "request.id")
  @Mapping(target = "operationId", source = "request.operationId")
  @Mapping(target = "lookupId", source = "request.lookup.id")
  @Mapping(target = "tenantId", source = "request.lookup.tenantId")
  @Mapping(target = "operation", source = "request.operation")
  @Mapping(target = "requestStatus", source = "request.requestStatus")
  @Mapping(target = "createdAt", source = "request.createdAt")
  @Mapping(target = "lookupType", source = "lookupType")
  LookupCacheRequest toRequest(LookupCacheRequestWithIndex lookupIndexCacheRequest);
}
