package com.databahn.planes.controlplane.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class CsvConfig {
  @JsonProperty("schema")
  private String schema;

  @JsonProperty("format")
  private String format;

  @JsonProperty("csv_fields")
  private List<CsvFields> csvFields;

  @Data
  public static class CsvFields {
    @JsonProperty("csv_index")
    int csvIndex;

    @JsonProperty("name")
    String name;

    @JsonProperty("csv_header")
    String csvHeader;
  }
}
