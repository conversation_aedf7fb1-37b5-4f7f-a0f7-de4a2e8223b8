package com.databahn.planes.controlplane.constants;

public class ReplayConstants {

  public static final String STATUS_PENDING = "PENDING";
  public static final String STATUS_FAILED = "FAILED";
  public static final String STATUS_COMPLETED = "COMPLETED";
  public static final String TOTAL_FILE_SIZE = "TOTAL_FILE_SIZE";

  public static final String STATUS_IN_PROGRESS = "IN_PROGRESS";

  public static final String STATUS_COMPLETED_WITH_ERROR = "COMPLETED_WITH_ERROR";
  public static final String STATUS_YET_TO_PROCESS = "YET_TO_PROCESS";
  public static final String STATUS_PROCESSING = "PROCESSING";
  public static final String STATUS_DOWNLOAD = "DOWNLOAD";
  public static final String STATUS_DOWNLOAD_FAILED = "DOWNLOAD_FAILED";

  public static final String TOTAL = "TOTAL";

  public static final String JOB_DELETE = "delete";

  public static final String BUCKET = "bucket";
  public static final String PATH = "path";
  public static final String ACCESS_KEY_ID = "access_key_id";
  public static final String SECRET_ACCESS_KEY = "secret_access_key";
  public static final String REGION = "region";
}
