package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.model.OpenSearchSearchResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.core.rest.RestStatus;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.index.query.QueryStringQueryBuilder;
import org.opensearch.search.SearchHit;
import org.opensearch.search.SearchHits;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class OpenSearchService {

  private RestHighLevelClient osClient;

  public OpenSearchSearchResponse search(
      String indexName,
      String query,
      int page,
      int size,
      String sort,
      SortOrder order,
      Object[] searchAfter) {
    try {
      SearchRequest searchRequest = new SearchRequest(indexName);
      SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
      if (StringUtils.isNotEmpty(sort)) {
        sourceBuilder.sort(sort, order.getClientValue());
        if (Objects.nonNull(searchAfter) && searchAfter.length > 0) {
          sourceBuilder.searchAfter(searchAfter);
        }
      }
      sourceBuilder.from(page * size);
      sourceBuilder.size(size);
      if (StringUtils.isNotEmpty(query)) {
        QueryBuilder queryBuilder = new QueryStringQueryBuilder(query);
        sourceBuilder.query(queryBuilder);
      }
      searchRequest.source(sourceBuilder);
      SearchResponse response = osClient.search(searchRequest, RequestOptions.DEFAULT);
      RestStatus status = response.status();
      if (status.getStatus() != 200) {
        log.error("failed to search from open search, status: {}", status.getStatus());
        throw new RuntimeException(
            "failed to search from open search with status: " + status.getStatus());
      }
      SearchHits hits = response.getHits();
      SearchHit[] searchHits = hits.getHits();
      long total = hits.getTotalHits().value;
      List<Map<String, Object>> data =
          Arrays.stream(searchHits).map(SearchHit::getSourceAsMap).collect(Collectors.toList());
      Object[] dataSearchAfter =
          searchHits.length == 0 ? null : searchHits[searchHits.length - 1].getSortValues();
      return new OpenSearchSearchResponse(data, total, dataSearchAfter);
    } catch (Exception e) {
      if (e.getMessage() != null && e.getMessage().contains("index_not_found")) {
        log.warn("index not found: {}", indexName);
        throw new RuntimeException("Index not found: " + indexName);
      }
      log.error("failed to search from open search", e);
      throw new RuntimeException("failed to search from open search", e);
    }
  }

  public enum SortOrder {
    ASC("asc"),
    DESC("desc");

    private final String value;

    SortOrder(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }

    public org.opensearch.search.sort.SortOrder getClientValue() {
      return org.opensearch.search.sort.SortOrder.fromString(value);
    }

    public static SortOrder fromString(String value) {
      for (SortOrder sortOrder : SortOrder.values()) {
        if (sortOrder.getValue().equalsIgnoreCase(value)) {
          return sortOrder;
        }
      }
      throw new IllegalArgumentException("invalid sort order: " + value);
    }
  }
}
