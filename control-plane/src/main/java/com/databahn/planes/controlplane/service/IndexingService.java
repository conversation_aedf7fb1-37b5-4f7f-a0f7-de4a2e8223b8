package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.constants.StatsConstants;
import com.databahn.planes.exception.ValidationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IndexingService {

  private final KafkaTemplate<String, String> kafkaTemplateAsync;
  private final KafkaTemplate<String, String> kafkaTemplateSync;
  private final ObjectMapper objectMapper;

  public IndexingService(
      @Qualifier("kafkaTemplateAsync") KafkaTemplate<String, String> kafkaTemplateAsync,
      @Qualifier("kafkaTemplateSync") KafkaTemplate<String, String> kafkaTemplateSync,
      ObjectMapper objectMapper) {
    this.kafkaTemplateAsync = kafkaTemplateAsync;
    this.kafkaTemplateSync = kafkaTemplateSync;
    this.objectMapper = objectMapper;
  }

  public void postToKafkaSync(String topic, String indexHeader, String key, Object object) {
    String message = toJson(object);
    try {
      List<Header> headers;
      if (indexHeader != null) {
        headers = List.of(new RecordHeader(StatsConstants.HEADER_INDEX, indexHeader.getBytes()));
      } else {
        headers = List.of();
      }
      ProducerRecord<String, String> record =
          new ProducerRecord<>(topic, null, null, key, message, headers);
      CompletableFuture<SendResult<String, String>> result = kafkaTemplateSync.send(record);
      result.get();
    } catch (InterruptedException | ExecutionException e) {
      log.error("failed to send to kafka", e);
      throw new RuntimeException(e);
    }
  }

  public void postToKafkaAsync(String topic, String indexHeader, String key, Object object) {
    String message = toJson(object);
    List<Header> headers;
    if (indexHeader != null) {
      headers = List.of(new RecordHeader(StatsConstants.HEADER_INDEX, indexHeader.getBytes()));
    } else {
      headers = List.of();
    }
    ProducerRecord<String, String> record =
        new ProducerRecord<>(topic, null, null, key, message, headers);
    CompletableFuture<SendResult<String, String>> result = kafkaTemplateAsync.send(record);
    result.exceptionallyAsync(
        e -> {
          log.error("failed to send to kafka", e);
          return null;
        });
  }

  private String toJson(Object object) {
    try {
      return objectMapper.writeValueAsString(object);
    } catch (JsonProcessingException e) {
      log.error("failed to parse stats", e);
      throw new ValidationException("Invalid stats", e);
    }
  }
}
