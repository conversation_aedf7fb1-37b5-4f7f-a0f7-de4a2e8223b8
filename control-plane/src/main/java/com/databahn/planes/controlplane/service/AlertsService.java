package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.data.elastic.AlertsRepository;
import com.databahn.planes.controlplane.service.mapper.AlertMapper;
import com.databahn.planes.model.alerts.Alert;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class AlertsService {

  private final AlertsRepository repository;
  private final AlertMapper alertMapper;

  public Map<Integer, Exception> upsertAlerts(List<Alert> alerts) {
    Map<Integer, Exception> results = Maps.newHashMap();
    if (CollectionUtils.isEmpty(alerts)) {
      return results;
    }
    for (int i = 0, alertsSize = alerts.size(); i < alertsSize; i++) {
      Alert alert = alerts.get(i);
      try {
        Optional<com.databahn.planes.controlplane.data.elastic.Alert> optionalAlert =
            repository.findById(alert.buildId());
        com.databahn.planes.controlplane.data.elastic.Alert entity;
        if (optionalAlert.isPresent()) {
          entity = optionalAlert.get();
          alertMapper.updateEntity(entity, alert);
        } else {
          entity = alertMapper.toEntity(alert);
        }
        repository.save(entity);
        results.put(i, null);
      } catch (Exception e) {
        log.error("failed to save alert to alert store", e);
        results.put(i, e);
      }
    }
    return results;
  }
}
