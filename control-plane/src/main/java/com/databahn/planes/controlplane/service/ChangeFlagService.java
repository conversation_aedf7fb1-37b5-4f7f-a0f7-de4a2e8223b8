package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.entity.ChangeFlagRequest;
import com.databahn.planes.controlplane.repo.ChangeFlagRepository;
import com.databahn.planes.controlplane.service.mapper.ChangeFlagMapper;
import com.databahn.planes.model.changeflag.ChangeFlag;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class ChangeFlagService {
  private ChangeFlagRepository changeFlagRepository;
  private ChangeFlagMapper changeFlagMapper;

  public List<ChangeFlag> getChangeFlags(
      String tenantId, UUID dataPlaneId, Long since, Pageable pageable) {
    List<ChangeFlagRequest> changeFlagRequests;
    if (since == null) {
      changeFlagRequests =
          changeFlagRepository.findByTenantIdAndDataPlaneIdAndBodyNotNullOrderBySequenceId(
              tenantId, dataPlaneId, pageable);
    } else {
      changeFlagRequests =
          changeFlagRepository
              .findByTenantIdAndDataPlaneIdAndSequenceIdGreaterThanAndBodyNotNullOrderBySequenceIdAsc(
                  tenantId, dataPlaneId, since, pageable);
    }
    Map<String, ChangeFlagRequest> changeFlagRequestMap =
        changeFlagRequests.stream()
            .collect(
                Collectors.toMap(ChangeFlagRequest::getEntityId, Function.identity(), (a, b) -> b));
    return changeFlagRequestMap.values().stream()
        .sorted(Comparator.comparing(ChangeFlagRequest::getSequenceId))
        .map(changeFlagMapper::changeFlagEntityToChangeFlag)
        .collect(Collectors.toList());
  }
}
