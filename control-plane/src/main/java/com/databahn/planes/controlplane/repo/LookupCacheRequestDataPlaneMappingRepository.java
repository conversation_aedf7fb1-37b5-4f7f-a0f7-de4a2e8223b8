package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.LookupCacheRequestDataPlaneMapping;
import com.databahn.planes.model.constants.LookupOperations;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface LookupCacheRequestDataPlaneMappingRepository
    extends JpaRepository<LookupCacheRequestDataPlaneMapping, UUID> {
  Optional<LookupCacheRequestDataPlaneMapping> findByRequestIdAndTenantIdAndDataPlaneIdAndOperation(
      UUID requestId, UUID tenantId, UUID dataPlaneId, LookupOperations operation);
}
