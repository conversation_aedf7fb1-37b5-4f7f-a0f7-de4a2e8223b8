package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.DataplaneService;
import com.databahn.planes.model.dataplane.DataPlane;
import com.databahn.planes.model.dataplane.Tenant;
import com.databahn.planes.response.Response;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/data_plane")
public class DataPlaneController {

  private DataplaneService dataPlaneService;

  @GetMapping("/tenants/{id}")
  public Response<List<Tenant>> getTenants(@PathVariable("id") UUID id) {
    List<Tenant> tenants = dataPlaneService.getTenants(id);
    return Response.Ok(tenants);
  }

  @PutMapping("/health")
  public Response<String> health(@RequestBody DataPlane dataPlane) {
    dataPlaneService.health(dataPlane.getDataPlaneId());
    return Response.Ok("Health check reported");
  }
}
