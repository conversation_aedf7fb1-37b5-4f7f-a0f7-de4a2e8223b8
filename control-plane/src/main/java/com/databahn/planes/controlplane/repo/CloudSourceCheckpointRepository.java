package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.CloudSourceCheckpoint;
import com.databahn.planes.controlplane.entity.CloudSourceCheckpointId;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CloudSourceCheckpointRepository
    extends JpaRepository<CloudSourceCheckpoint, CloudSourceCheckpointId> {

  Optional<CloudSourceCheckpoint> findByTenantIdAndSourceId(UUID tenantId, UUID sourceId);

  @Query(
      "SELECT c FROM CloudSourceCheckpoint c WHERE c.tenantId IN :tenantIds AND c.sourceId IN :sourceIds")
  List<CloudSourceCheckpoint> findByTenantIdsAndSourceIds(
      @Param("tenantIds") List<UUID> tenantIds, @Param("sourceIds") List<UUID> sourceIds);
}
