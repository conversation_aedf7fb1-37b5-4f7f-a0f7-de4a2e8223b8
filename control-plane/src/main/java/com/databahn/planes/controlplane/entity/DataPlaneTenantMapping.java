package com.databahn.planes.controlplane.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@Entity
@Table(name = "data_plane_tenant_mappings")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class DataPlaneTenantMapping {

  @EqualsAndHashCode.Include @EmbeddedId private DataPlaneTenantMappingId id;

  @ManyToOne()
  @MapsId("dataPlaneId")
  @JoinColumn(name = "data_plane_id", nullable = false)
  private DataPlane dataPlane;

  @ToString.Exclude
  @ManyToOne()
  @MapsId("tenantId")
  @JoinColumn(name = "tenant_id", nullable = false)
  private Tenant tenant;
}
