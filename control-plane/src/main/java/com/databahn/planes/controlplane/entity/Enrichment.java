package com.databahn.planes.controlplane.entity;

import com.databahn.planes.controlplane.model.EnrichmentConfig;
import com.databahn.planes.model.constants.Status;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import java.util.Date;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Enrichment {

  @Id
  @UuidGenerator
  @Column(name = "id")
  @EqualsAndHashCode.Include
  private UUID id;

  @ManyToOne(fetch = FetchType.EAGER, optional = false)
  @JoinColumn(name = "lookup_id", nullable = false)
  private Lookup lookup;

  @Column(name = "tenant_id")
  @JsonProperty("tenant_id")
  private UUID tenantId;

  @Column(name = "source_id")
  @JsonProperty("source_id")
  private UUID sourceId;

  @Column(name = "destination_id")
  @JsonProperty("destination_id")
  private UUID destinationId;

  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "description")
  private String description;

  @Column(updatable = false)
  @CreationTimestamp
  private Date createdAt;

  @Column(name = "created_by", length = 100, nullable = false)
  private UUID createdBy;

  @Column(name = "updated_by", length = 100, nullable = false)
  private UUID updatedBy;

  @UpdateTimestamp private Date updatedAt;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private Status status;

  @Column(name = "cache_request_id")
  private UUID cacheRequestId;

  @Column(name = "config", columnDefinition = "json")
  @JdbcTypeCode(SqlTypes.JSON)
  private EnrichmentConfig config;

  @ManyToOne
  @EqualsAndHashCode.Exclude
  @ToString.Exclude
  @JoinColumn(name = "pipeline_id", referencedColumnName = "id")
  private Pipeline pipeline;

  @ManyToOne()
  @JoinColumn(name = "data_plane_id", referencedColumnName = "id")
  private DataPlane dataPlane;
}
