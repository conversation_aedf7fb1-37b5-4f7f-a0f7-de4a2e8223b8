package com.databahn.planes.controlplane.mappers;

import com.databahn.planes.controlplane.entity.Enrichment;
import com.databahn.planes.controlplane.entity.EnrichmentChangeFlag;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    componentModel = "spring")
public interface EnrichmentMapper {
  EnrichmentMapper INSTANCE = Mappers.getMapper(EnrichmentMapper.class);

  @Mapping(target = "pipelineId", source = "enrichment.pipeline.id")
  @Mapping(target = "lookupId", source = "enrichment.lookup.id")
  @Mapping(target = "lookupName", source = "enrichment.lookup.name")
  @Mapping(target = "lookupCsvConfig", source = "enrichment.lookup.csvConfig")
  EnrichmentChangeFlag enrichmentToEnrichmentChangeFlag(Enrichment enrichment);
}
