package com.databahn.planes.controlplane.service;

import com.databahn.planes.constants.ChangeFlagAction;
import com.databahn.planes.constants.ChangeFlagType;
import com.databahn.planes.controlplane.common.ChangeFlagEntity;
import com.databahn.planes.controlplane.common.ChangeFlagParentEntity;
import com.databahn.planes.controlplane.entity.ChangeFlagRequest;
import com.databahn.planes.controlplane.repo.ChangeFlagRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.Instant;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ChangeFlagProducer {

  private ChangeFlagRepository changeFlagRequestRepository;
  private ObjectMapper objectMapper;
  private static final String VERSION = "v1";

  public ChangeFlagProducer(
      ChangeFlagRepository changeFlagRequestRepository, ObjectMapper objectMapper) {
    this.changeFlagRequestRepository = changeFlagRequestRepository;
    this.objectMapper = objectMapper;
  }

  public List<String> sendChangeFlag(
      String tenantId,
      String dataPlaneId,
      ChangeFlagType flagType,
      ChangeFlagAction action,
      String id,
      ChangeFlagEntity entity) {
    List<ChangeFlagRequest> changeFlagRecords = new ArrayList<>();
    ChangeFlagRequest cfRecord =
        buildChangeFlagRecord(tenantId, dataPlaneId, flagType, action, id, entity);
    changeFlagRecords.add(cfRecord);
    List<ChangeFlagRequest> savedRequests = changeFlagRequestRepository.saveAll(changeFlagRecords);
    return savedRequests.stream().map(ChangeFlagRequest::getRequestId).toList();
  }

  private ChangeFlagRequest buildChangeFlagRecord(
      String tenantId,
      String dataPlaneId,
      ChangeFlagType flagType,
      ChangeFlagAction action,
      String id,
      ChangeFlagEntity entity) {
    ChangeFlagRequest changeFlagRequest = new ChangeFlagRequest();
    ChangeFlagParentEntity cf = new ChangeFlagParentEntity();
    cf.setEntity(entity);
    changeFlagRequest.setTenantId(tenantId);
    changeFlagRequest.setDataPlaneId(UUID.fromString(dataPlaneId));
    changeFlagRequest.setEntityType(flagType.getChangeFlagType());
    changeFlagRequest.setAction(action.getAction());
    changeFlagRequest.setEntityId(id);
    Map<String, Object> map = objectMapper.convertValue(cf, Map.class);
    changeFlagRequest.setBody(map);
    changeFlagRequest.setSchemaVersion(VERSION);
    changeFlagRequest.setRequestId(UUID.randomUUID().toString());
    changeFlagRequest.setTimestamp(Instant.now());
    return changeFlagRequest;
  }
}
