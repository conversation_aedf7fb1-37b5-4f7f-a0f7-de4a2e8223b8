package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.entity.DataPlaneTenantMapping;
import com.databahn.planes.controlplane.entity.Tenant;
import com.databahn.planes.controlplane.repo.CloudSourceCheckpointRepository;
import com.databahn.planes.controlplane.repo.TenantRepository;
import com.databahn.planes.model.checkpoint.CheckpointsRequest;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpoint;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpointRequest;
import java.time.Instant;
import java.util.*;
import org.springframework.stereotype.Service;

@Service
public class CloudSourceCheckpointService {

  private final CloudSourceCheckpointRepository checkpointRepository;
  private final TenantRepository tenantRepository;

  public CloudSourceCheckpointService(
      CloudSourceCheckpointRepository checkpointRepository, TenantRepository tenantRepository) {
    this.checkpointRepository = checkpointRepository;
    this.tenantRepository = tenantRepository;
  }

  public Optional<CloudSourceCheckpoint> get(UUID tenantId, UUID sourceId) {
    Optional<com.databahn.planes.controlplane.entity.CloudSourceCheckpoint> checkpoint =
        checkpointRepository.findByTenantIdAndSourceId(tenantId, sourceId);
    return checkpoint.map(CloudSourceCheckpointService::mapToModel);
  }

  public List<CloudSourceCheckpoint> get(CheckpointsRequest request) {
    if (request == null || request.getIds() == null || request.getIds().isEmpty()) {
      return new ArrayList<>();
    }
    List<UUID> tenantIds = new ArrayList<>();
    List<UUID> sourceIds = new ArrayList<>();
    request
        .getIds()
        .forEach(
            id -> {
              tenantIds.add(id.getTenantId());
              sourceIds.add(id.getSourceId());
            });
    List<com.databahn.planes.controlplane.entity.CloudSourceCheckpoint> checkpoints =
        checkpointRepository.findByTenantIdsAndSourceIds(tenantIds, sourceIds);
    return checkpoints.stream().map(CloudSourceCheckpointService::mapToModel).toList();
  }

  public void saveCheckpoint(CloudSourceCheckpointRequest checkpoint) {
    validateCheckpoint(checkpoint);
    Optional<com.databahn.planes.controlplane.entity.CloudSourceCheckpoint> existingCheckpoint =
        checkpointRepository.findByTenantIdAndSourceId(
            checkpoint.getTenantId(), checkpoint.getSourceId());
    Instant checkPointTime = Instant.ofEpochMilli(checkpoint.getTimestamp());
    if (existingCheckpoint.isPresent()) {
      if (checkpoint.isAddOnly()) {
        throw new IllegalArgumentException("Checkpoint already exists");
      }
      com.databahn.planes.controlplane.entity.CloudSourceCheckpoint checkpointToUpdate =
          existingCheckpoint.get();
      checkpointToUpdate.setCheckpoint(checkpoint.getCheckpoint());
      checkpointToUpdate.setUpdatedAt(checkPointTime);
      checkpointRepository.save(checkpointToUpdate);
    } else {
      com.databahn.planes.controlplane.entity.CloudSourceCheckpoint checkpointToSave =
          new com.databahn.planes.controlplane.entity.CloudSourceCheckpoint();
      checkpointToSave.setTenantId(checkpoint.getTenantId());
      checkpointToSave.setSourceId(checkpoint.getSourceId());
      checkpointToSave.setDataPlaneId(checkpoint.getDataPlaneId());
      checkpointToSave.setCheckpoint(checkpoint.getCheckpoint());
      checkpointToSave.setCreatedAt(checkPointTime);
      checkpointToSave.setUpdatedAt(checkPointTime);
      checkpointRepository.save(checkpointToSave);
    }
  }

  private void validateCheckpoint(CloudSourceCheckpoint checkpoint) {
    if (checkpoint == null) {
      throw new IllegalArgumentException("Checkpoint cannot be null");
    }
    if (checkpoint.getTenantId() == null) {
      throw new IllegalArgumentException("TenantId cannot be null");
    }
    if (checkpoint.getSourceId() == null) {
      throw new IllegalArgumentException("SourceId cannot be null");
    }
    if (checkpoint.getDataPlaneId() == null) {
      throw new IllegalArgumentException("DataPlaneId cannot be null");
    }
    if (checkpoint.getCheckpoint() == null || checkpoint.getCheckpoint().isEmpty()) {
      throw new IllegalArgumentException("Checkpoint cannot be null or empty");
    }
    if (checkpoint.getTimestamp() == null) {
      throw new IllegalArgumentException("Timestamp cannot be null");
    }
    Optional<Tenant> tenantById = tenantRepository.findById(checkpoint.getTenantId());
    if (tenantById.isEmpty()) {
      throw new IllegalArgumentException("Tenant not found by id:" + checkpoint.getTenantId());
    }
    Set<DataPlaneTenantMapping> dataPlaneTenantMappings =
        tenantById.get().getDataPlaneTenantMappings();
    if (dataPlaneTenantMappings.stream()
        .noneMatch(mapping -> mapping.getDataPlane().getId().equals(checkpoint.getDataPlaneId()))) {
      throw new IllegalArgumentException(
          "DataPlaneId not found for tenant:"
              + checkpoint.getTenantId()
              + " with dataPlaneId:"
              + checkpoint.getDataPlaneId());
    }
  }

  private static CloudSourceCheckpoint mapToModel(
      com.databahn.planes.controlplane.entity.CloudSourceCheckpoint checkpointEntity) {
    CloudSourceCheckpoint cloudSourceCheckpoint = new CloudSourceCheckpoint();
    cloudSourceCheckpoint.setTenantId(checkpointEntity.getTenantId());
    cloudSourceCheckpoint.setSourceId(checkpointEntity.getSourceId());
    cloudSourceCheckpoint.setDataPlaneId(checkpointEntity.getDataPlaneId());
    cloudSourceCheckpoint.setCheckpoint(checkpointEntity.getCheckpoint());
    cloudSourceCheckpoint.setTimestamp(checkpointEntity.getUpdatedAt().toEpochMilli());
    return cloudSourceCheckpoint;
  }
}
