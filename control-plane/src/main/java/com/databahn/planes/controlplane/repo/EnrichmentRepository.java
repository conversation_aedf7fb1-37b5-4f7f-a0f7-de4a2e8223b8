package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.Enrichment;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface EnrichmentRepository extends JpaRepository<Enrichment, UUID> {

  Optional<Enrichment> findByCacheRequestId(UUID cacheRequestId);

  List<Enrichment> findByLookup_IdAndTenantIdAndDataPlane_Id(
      UUID lookupId, UUID tenantId, UUID dataPlaneId);
}
