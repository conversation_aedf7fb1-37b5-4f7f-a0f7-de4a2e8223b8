package com.databahn.planes.controlplane.entity;

import com.databahn.planes.controlplane.common.InsightsRuleAttributes;
import com.databahn.planes.model.constants.LookupType;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UuidGenerator;
import org.hibernate.type.SqlTypes;

@Entity
@Data
@Table(name = "insights_rule")
public class InsightsRule {
  @Id @UuidGenerator private UUID id;

  @Column(name = "name", length = 100, nullable = false, unique = true)
  private String name;

  @Column(name = "tenant_id")
  private UUID tenantId;

  @Column(name = "attributes", columnDefinition = "json")
  @JdbcTypeCode(SqlTypes.JSON)
  private InsightsRuleAttributes attributes;

  @Column(name = "define_lookup")
  private Boolean defineLookup;

  @Column(name = "lookup_id")
  private UUID lookupId;

  @Column(name = "lookup_type")
  @Enumerated(EnumType.STRING)
  private LookupType lookupType;

  @ManyToOne()
  @JoinColumn(name = "data_plane_id", referencedColumnName = "id")
  private DataPlane dataPlane;
}
