package com.databahn.planes.controlplane.entity;

import jakarta.persistence.*;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@DynamicInsert
@Table(name = "change_flag_requests")
public class ChangeFlagRequest {
  @Id
  @Column(name = "request_id")
  private String requestId;

  @Column(name = "entity_id")
  private String entityId;

  private Instant timestamp;

  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "data_plane_id")
  private UUID dataPlaneId;

  @Column(name = "entity_type")
  private String entityType;

  @Column(name = "action")
  private String action;

  @Column(name = "is_processed")
  private Boolean isProcessed = false;

  @Column(name = "schema_version")
  private String schemaVersion;

  @Column(name = "sequence_id")
  private Long sequenceId;

  @Column(name = "body", columnDefinition = "json")
  @JdbcTypeCode(SqlTypes.JSON)
  private Map<String, Object> body;
}
