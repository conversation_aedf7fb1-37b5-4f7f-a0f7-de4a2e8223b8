package com.databahn.planes.controlplane.config;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KafkaFlusher {

  private final KafkaTemplate<String, String> kafkaTemplate;

  public KafkaFlusher(
      @Qualifier("kafkaTemplateAsync") KafkaTemplate<String, String> kafkaTemplate) {
    this.kafkaTemplate = kafkaTemplate;
  }

  @PreDestroy
  public void flush() {
    kafkaTemplate.flush();
    log.info("flushed async kafka template producer");
  }
}
