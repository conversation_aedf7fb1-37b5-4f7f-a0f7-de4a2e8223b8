package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.CloudSourceCheckpointService;
import com.databahn.planes.model.checkpoint.CheckpointsRequest;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpoint;
import com.databahn.planes.model.checkpoint.CloudSourceCheckpointRequest;
import com.databahn.planes.response.Response;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController()
@RequestMapping("/v1/cloud-source-checkpoint")
public class CloudSourceCheckpointController {

  private CloudSourceCheckpointService service;

  public CloudSourceCheckpointController(CloudSourceCheckpointService service) {
    this.service = service;
  }

  @GetMapping
  public Response<CloudSourceCheckpoint> get(
      @RequestParam UUID tenantId, @RequestParam UUID sourceId) {
    Optional<CloudSourceCheckpoint> sourceCheckpoint = service.get(tenantId, sourceId);
    return sourceCheckpoint
        .map(Response::Ok)
        .orElseGet(() -> Response.Message("Checkpoint not found"));
  }

  @PostMapping("/find")
  public Response<List<CloudSourceCheckpoint>> getCheckpoints(
      @RequestBody CheckpointsRequest request) {
    List<CloudSourceCheckpoint> checkpoints = service.get(request);
    return Response.Ok(checkpoints);
  }

  @PostMapping
  public Response<?> saveCheckpoint(@RequestBody CloudSourceCheckpointRequest checkpoint) {
    service.saveCheckpoint(checkpoint);
    log.info(
        "Cloud source checkpoint saved for tenantId: {} and sourceId: {}",
        checkpoint.getTenantId(),
        checkpoint.getSourceId());
    return Response.Message("Checkpoint saved");
  }
}
