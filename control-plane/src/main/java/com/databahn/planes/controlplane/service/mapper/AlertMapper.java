package com.databahn.planes.controlplane.service.mapper;

import org.mapstruct.BeforeMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public abstract class AlertMapper {

  @BeforeMapping
  protected void buildId(
      com.databahn.planes.model.alerts.Alert dto,
      @MappingTarget com.databahn.planes.controlplane.data.elastic.Alert entity) {
    entity.setId(dto.buildId());
  }

  public abstract com.databahn.planes.model.alerts.Alert toDto(
      com.databahn.planes.controlplane.data.elastic.Alert entity);

  public abstract com.databahn.planes.controlplane.data.elastic.Alert toEntity(
      com.databahn.planes.model.alerts.Alert dto);

  @Mapping(target = "alertEntity.dismissed", source = "alertDto.dismissed")
  @Mapping(target = "alertEntity.lastObservedAt", source = "alertDto.lastObservedAt")
  @Mapping(target = "alertEntity.updatedAt", source = "alertDto.updatedAt")
  @Mapping(target = "alertEntity.updatedBy", source = "alertDto.updatedBy")
  @Mapping(target = "alertEntity.status", source = "alertDto.status")
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "title", ignore = true)
  @Mapping(target = "criticality", ignore = true)
  @Mapping(target = "message", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "firstObservedAt", ignore = true)
  @Mapping(target = "functionalityType", ignore = true)
  @Mapping(target = "functionality", ignore = true)
  @Mapping(target = "functionalityEntityId", ignore = true)
  @Mapping(target = "functionalityEntityName", ignore = true)
  @Mapping(target = "alertType", source = "alertDto.alertType")
  @Mapping(target = "dataPlaneId", source = "alertDto.dataPlaneId")
  @Mapping(target = "errorMessage", source = "alertDto.errorMessage")
  @Mapping(target = "errorCode", source = "alertDto.errorCode")
  public abstract void updateEntity(
      @MappingTarget com.databahn.planes.controlplane.data.elastic.Alert alertEntity,
      com.databahn.planes.model.alerts.Alert alertDto);
}
