package com.databahn.planes.controlplane.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.util.Date;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

@Data
@Entity
@Table(name = "replay_job_executions")
@NoArgsConstructor
@AllArgsConstructor
public class ReplayJobExecution {

  @Id
  @UuidGenerator
  @Column(name = "id")
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "job_id", nullable = false)
  @JsonIgnore
  private ReplayJob job;

  @Column(name = "part_id", nullable = false)
  private UUID partId;

  @Column(name = "start_time")
  private Date startTime;

  @Column(name = "end_time")
  private Date endTime;

  @Column(name = "status")
  private String status;

  @Column(name = "filename", nullable = false)
  @NotBlank(message = "filename cannot be blank.")
  private String fileName;

  @Column(name = "error")
  private String error;

  @Column(name = "created_on")
  @UpdateTimestamp
  private Date createdOn;

  @Column(name = "last_updated_on")
  @UpdateTimestamp
  private Date lastUpdated;

  @Column(name = "progress")
  private Float progress;

  @Column(name = "lines")
  private Long lines;

  @Column(name = "size")
  private Long size;
}
