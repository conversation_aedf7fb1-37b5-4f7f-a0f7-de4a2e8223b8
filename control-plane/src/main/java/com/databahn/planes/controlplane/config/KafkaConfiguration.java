package com.databahn.planes.controlplane.config;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.*;
import org.springframework.util.backoff.FixedBackOff;

@Configuration
@EnableKafka
public class KafkaConfiguration {

  @Value("${kafka.bootstrap_brokers}")
  private String kafkaBrokers;

  private static final long RETRY_SECONDS = 2 * 60L;
  private static final long RECOVERY_SECONDS = 60 * 60 * 24L;

  @Bean
  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>>
      kafkaListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, String> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(consumerFactory());
    factory.getContainerProperties().setPollTimeout(3000);
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
    factory.setCommonErrorHandler(kafkaCommonErrorHandler());
    return factory;
  }

  @Bean
  public ConsumerFactory<String, String> consumerFactory() {
    return new DefaultKafkaConsumerFactory<>(consumerConfigs());
  }

  @Bean
  public Map<String, Object> consumerConfigs() {
    Map<String, Object> props = new HashMap<>();
    props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
    props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
    props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5000);
    props.put(
        ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
        "org.apache.kafka.clients.consumer.RoundRobinAssignor");
    props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    return props;
  }

  @Bean
  public ProducerFactory<String, String> producerFactoryAsync() {
    return new DefaultKafkaProducerFactory<>(producerConfigsAsync());
  }

  @Bean
  public ProducerFactory<String, String> producerFactorySync() {
    return new DefaultKafkaProducerFactory<>(producerConfigsSync());
  }

  @Bean
  public Map<String, Object> producerConfigsAsync() {
    Map<String, Object> props = new HashMap<>();
    props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
    props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.ACKS_CONFIG, "all");
    props.put(ProducerConfig.LINGER_MS_CONFIG, "10");
    props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 10485760);
    props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    return props;
  }

  @Bean
  public Map<String, Object> producerConfigsSync() {
    Map<String, Object> props = new HashMap<>();
    props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
    props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 10485760);
    return props;
  }

  private CommonErrorHandler kafkaCommonErrorHandler() {
    Logger logger = LoggerFactory.getLogger("KafkaErrorHandler");
    DefaultErrorHandler defaultErrorHandler =
        new DefaultErrorHandler(
            (r, e) -> {
              long offset = r.offset();
              int partition = r.partition();
              String topic = r.topic();
              String message =
                  String.format(
                      "Unprocessed: Failed to handle kafka message %s, offset %d, partition %d, topic %s",
                      offset, partition, topic);
              logger.error(message, e);
            },
            new FixedBackOff(RETRY_SECONDS * 1000L, RECOVERY_SECONDS / RETRY_SECONDS));
    return defaultErrorHandler;
  }

  @Bean(name = "kafkaTemplateSync")
  public KafkaTemplate<String, String> kafkaTemplateSync() {
    return new KafkaTemplate<>(producerFactorySync());
  }

  @Bean(name = "kafkaTemplateAsync")
  public KafkaTemplate<String, String> kafkaTemplateAsync() {
    return new KafkaTemplate<>(producerFactoryAsync());
  }

  @Bean(name = "kafkaStringTemplate")
  public KafkaTemplate<String, String> kafkaStringTemplate() {
    return new KafkaTemplate<>(producerFactorySync());
  }

  @Bean
  public KafkaListenerErrorHandler kafkaListenerErrorHandler() {
    return new KafkaErrorHandler();
  }
}
