package com.databahn.planes.controlplane.entity;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.UuidGenerator;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "Tenants")
public class Tenant {
  @Id @UuidGenerator private UUID id;

  @Column(name = "name", unique = true, nullable = false)
  private String name;

  @OneToMany(mappedBy = "tenant")
  private Set<DataPlaneTenantMapping> dataPlaneTenantMappings = new HashSet<>();
}
