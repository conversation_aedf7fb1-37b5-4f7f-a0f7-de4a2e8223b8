package com.databahn.planes.controlplane.entity;

import com.databahn.planes.model.constants.LookupOperations;
import com.databahn.planes.model.constants.Status;
import jakarta.persistence.*;
import java.util.Date;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

@Data
@Entity
@Table(name = "lookup_cache_request_data_plane_mapping")
public class LookupCacheRequestDataPlaneMapping {

  @Id
  @Column(name = "id")
  private UUID id;

  @Column(name = "request_id")
  private UUID requestId;

  @Column(name = "operation")
  @Enumerated(EnumType.STRING)
  private LookupOperations operation;

  @Column(name = "tenant_id")
  private UUID tenantId;

  @Column(name = "data_plane_id")
  private UUID dataPlaneId;

  @Column(name = "created_at", updatable = false)
  @CreationTimestamp
  private Date createdAt;

  @Column(name = "error_message")
  private String errorMessage;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private Status status;
}
