package com.databahn.planes.controlplane.entity;

import com.databahn.planes.controlplane.model.CsvConfig;
import com.databahn.planes.model.constants.LookupType;
import com.databahn.planes.model.constants.Status;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.util.Date;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@Table(
    uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "tenant_id"})},
    name = "lookup")
public class Lookup {

  @Id
  @Column(name = "id")
  private UUID id;

  @Column(name = "tenant_id")
  @JsonProperty("tenant_id")
  private UUID tenantId;

  @Column(name = "name", nullable = false)
  @NotBlank(message = "Name cannot be blank")
  private String name;

  @Column(name = "description")
  private String description;

  @Column(name = "is_dynamic")
  private Boolean isDynamic = false;

  @Column(name = "index_request_id")
  private UUID indexRequestId;

  @Column(name = "index_name")
  @JsonProperty("index_name")
  private String indexName;

  @Column(name = "cache_request_id")
  private UUID cacheRequestId;

  @Column(updatable = false)
  @CreationTimestamp
  private Date createdAt;

  @Column(name = "csv_config", columnDefinition = "json")
  @JdbcTypeCode(SqlTypes.JSON)
  private CsvConfig csvConfig;

  @UpdateTimestamp private Date updatedAt;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private Status status;

  @Column(name = "type")
  @Enumerated(EnumType.STRING)
  private LookupType type;
}
