package com.databahn.planes.controlplane.entity;

import jakarta.persistence.*;
import java.time.Instant;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

@Data
@Entity
@Table(name = "data_planes")
public class DataPlane {
  @Id @UuidGenerator private UUID id;

  @Column(name = "created_at")
  @CreationTimestamp
  private Date createdAt;

  @Column(name = "updated_at")
  @UpdateTimestamp
  private Date updatedAt;

  @Column(name = "cp_gateway_url")
  private String cpGatewayUrl;

  @Column(name = "dp_gateway_url")
  private String dpGatewayUrl;

  @Column(name = "dp_controller_url")
  private String dpControllerUrl;

  @Column(name = "heartbeat_at")
  private Instant heartbeatAt;

  @OneToMany(mappedBy = "dataPlane")
  private Set<DataPlaneTenantMapping> dataPlaneTenantMappings = new HashSet<>();
}
