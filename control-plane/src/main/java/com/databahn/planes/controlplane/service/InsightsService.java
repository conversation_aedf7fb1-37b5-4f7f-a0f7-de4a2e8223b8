package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.common.InsightsRuleAttributes;
import com.databahn.planes.controlplane.constants.StatsConstants;
import com.databahn.planes.controlplane.entity.Lookup;
import com.databahn.planes.model.constants.LookupType;
import com.databahn.planes.model.insights.StagingInsight;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class InsightsService {

  private static final String INDEX_PATTERN = "yyyy_MM_dd_HH";
  private static final String INSIGHTS_STAGING_INDEX_PREFIX = "db_staging_insights_v1_";
  private final IndexingService indexingService;
  private final InsightsRuleService insightsRuleService;
  private static final Long INDEX_WINDOW = (long) (60 * 60 * 1000);

  public InsightsService(IndexingService indexingService, InsightsRuleService insightsRuleService) {
    this.indexingService = indexingService;
    this.insightsRuleService = insightsRuleService;
  }

  public void postInsightsToKafka(List<StagingInsight> insights) {
    insights.forEach(
        insight -> {
          Long timeUnix = insight.maxTime();
          String indexSuffix = indexSuffix(timeUnix);
          String index =
              INSIGHTS_STAGING_INDEX_PREFIX
                  + insight.type()
                  + "_"
                  + indexSuffix
                  + "_"
                  + insight.tenantId();
          log.info("Posting insight to kafka: {}", index);
          this.indexingService.postToKafkaSync(
              StatsConstants.TOPIC_INDEXING_OPENSEARCH, index, null, insight);
          if (StringUtils.isNotEmpty(insight.lookupId())) {
            try {
              handleLookupFromInsight(insight);
            } catch (Exception e) {
              log.error("Error while posting insights lookup " + insight.type(), e);
            }
          }
        });
  }

  private void handleLookupFromInsight(StagingInsight insight) throws NoSuchAlgorithmException {
    Optional<InsightsRuleAttributes> attributes =
        insightsRuleService.getAttributes(UUID.fromString(insight.type()));
    if (attributes.isPresent()) {
      Optional<Lookup> lookup =
          this.insightsRuleService.getDynamicLookup(UUID.fromString(insight.lookupId()));
      if (lookup.isPresent()) {
        Lookup matchingLookup = lookup.get();
        List<String> ruleAttributes = attributes.get().getAttributes();
        DynamicLookupValue dynamicLookupValue = new DynamicLookupValue();
        dynamicLookupValue.insightRuleId = insight.type();
        dynamicLookupValue.lookupId = insight.lookupId();
        dynamicLookupValue.tenantId = insight.tenantId();
        dynamicLookupValue.timestamp = Instant.now().toEpochMilli();
        dynamicLookupValue.data = new HashMap<>();
        if (matchingLookup.getType() == LookupType.VOLUME_CONTROLLER) {
          dynamicLookupValue.data.put("value", insight.key1());
        } else {
          for (int i = 0; i < ruleAttributes.size(); i++) {
            switch (i) {
              case 0 -> dynamicLookupValue.data.put(ruleAttributes.get(i), insight.key1());
              case 1 -> dynamicLookupValue.data.put(ruleAttributes.get(i), insight.key2());
              case 2 -> dynamicLookupValue.data.put(ruleAttributes.get(i), insight.key3());
              case 3 -> dynamicLookupValue.data.put(ruleAttributes.get(i), insight.key4());
              case 4 -> dynamicLookupValue.data.put(ruleAttributes.get(i), insight.key5());
            }
          }
        }
        String id = buildId(insight);
        dynamicLookupValue.id = id;
        this.indexingService.postToKafkaAsync(
            StatsConstants.TOPIC_INDEXING_LOOKUP,
            matchingLookup.getIndexName(),
            id,
            dynamicLookupValue);
      } else {
        log.error("RequestId not found for lookupId: {}", insight.lookupId());
      }
    } else {
      log.error("Insights rule not found for id: {}", insight.type());
    }
  }

  private static String buildId(StagingInsight insight) throws NoSuchAlgorithmException {
    String idData =
        String.format(
            "%s,%s,%s,%s,%s,%s,%s,%s",
            insight.tenantId(),
            insight.lookupId(),
            insight.sourceId(),
            insight.key1(),
            insight.key2(),
            insight.key3(),
            insight.key4(),
            insight.key5());
    byte[] digest = MessageDigest.getInstance("MD5").digest(idData.getBytes());
    StringBuilder hexString = new StringBuilder();
    for (byte b : digest) {
      hexString.append(String.format("%02x", b));
    }
    return hexString.toString();
  }

  public static class DynamicLookupValue {
    @JsonProperty("id")
    private String id;

    @JsonProperty("insight_rule_id")
    private String insightRuleId;

    @JsonProperty("lookup_id")
    private String lookupId;

    @JsonProperty("tenant_id")
    private String tenantId;

    @JsonProperty("timestamp")
    private Long timestamp;

    @JsonProperty("data")
    private Map<String, String> data;
  }

  private String indexSuffix(Long time) {
    long windowDivider = time / INDEX_WINDOW;
    long start = INDEX_WINDOW * windowDivider;
    long end = start + INDEX_WINDOW;
    LocalDateTime endTime = Instant.ofEpochMilli(end).atZone(ZoneOffset.UTC).toLocalDateTime();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(INDEX_PATTERN);
    return endTime.format(formatter);
  }
}
