package com.databahn.planes.controlplane.entity;

import com.databahn.planes.controlplane.common.ChangeFlagEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Data;

@Data
public class LookupChangeFlag implements ChangeFlagEntity {
  private String id;

  @JsonProperty("tenant_id")
  private String tenantId;

  @JsonProperty("request_id")
  private String requestId;

  @Override
  public UUID getSourceId() {
    return null;
  }

  @Override
  public boolean sendPipelineChangeFlag() {
    return false;
  }
}
