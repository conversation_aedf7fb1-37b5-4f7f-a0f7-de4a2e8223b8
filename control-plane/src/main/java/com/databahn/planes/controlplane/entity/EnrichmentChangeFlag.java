package com.databahn.planes.controlplane.entity;

import com.databahn.planes.controlplane.common.ChangeFlagEntity;
import com.databahn.planes.controlplane.model.CsvConfig;
import com.databahn.planes.controlplane.model.EnrichmentConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.Data;

@Data
public class EnrichmentChangeFlag implements ChangeFlagEntity {
  private String id;
  private String name;
  private String description;

  @JsonProperty("source_id")
  private String sourceId;

  @JsonProperty("pipeline_id")
  private String pipelineId;

  @JsonProperty("destination_id")
  private String destinationId;

  @JsonProperty("tenant_id")
  private String tenantId;

  @JsonProperty("cache_request_id")
  private String cacheRequestId;

  private EnrichmentConfig config;

  @JsonProperty("lookup_name")
  private String lookupName;

  @JsonProperty("lookup_id")
  private String lookupId;

  @JsonProperty("lookup_csv_config")
  private CsvConfig lookupCsvConfig;

  @JsonProperty("referenced_attributes")
  private List<String> referencedAttributes;

  @Override
  public boolean sendPipelineChangeFlag() {
    return true;
  }

  @Override
  public UUID getSourceId() {
    return UUID.fromString(sourceId);
  }
}
