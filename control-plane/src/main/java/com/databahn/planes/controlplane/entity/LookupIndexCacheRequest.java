package com.databahn.planes.controlplane.entity;

import com.databahn.planes.model.constants.LookupOperations;
import com.databahn.planes.model.constants.LookupRequestType;
import com.databahn.planes.model.constants.Status;
import jakarta.persistence.*;
import java.util.Date;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Data
@Entity
@Table(name = "lookup_index_cache_request")
@IdClass(LookupIndexCacheRequestKey.class)
public class LookupIndexCacheRequest {

  @Id
  @Column(name = "id")
  private UUID id;

  @Column(name = "operation_id")
  private UUID operationId;

  @ManyToOne()
  @JoinColumn(name = "lookup_id", nullable = false)
  private Lookup lookup;

  @Column(name = "type")
  @Enumerated(EnumType.STRING)
  private LookupRequestType type;

  @Id
  @Column(name = "operation")
  @Enumerated(EnumType.STRING)
  private LookupOperations operation;

  @Column(updatable = false)
  @CreationTimestamp
  private Date createdAt;

  @UpdateTimestamp private Date updatedAt;

  @Column(name = "file_name")
  private String fileName;

  @Column(name = "file_path")
  private String filePath;

  @Column(name = "error_message")
  private String errorMessage;

  @Column(name = "file_storage")
  private String fileStorage;

  @Column(name = "request_status")
  @Enumerated(EnumType.STRING)
  private Status requestStatus;

  @Column(name = "request_action")
  private String requestAction;

  @Column(name = "index_name")
  private String indexName;
}
