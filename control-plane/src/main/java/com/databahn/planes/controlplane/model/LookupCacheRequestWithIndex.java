package com.databahn.planes.controlplane.model;

import com.databahn.planes.controlplane.entity.LookupIndexCacheRequest;
import com.databahn.planes.model.constants.LookupType;
import lombok.Data;

@Data
public class LookupCacheRequestWithIndex {

  private LookupIndexCacheRequest request;
  private LookupType lookupType;

  public LookupCacheRequestWithIndex(LookupIndexCacheRequest request, LookupType lookupType) {
    this.request = request;
    this.lookupType = lookupType;
  }
}
