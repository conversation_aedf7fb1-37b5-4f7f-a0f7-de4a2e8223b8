package com.databahn.planes.controlplane.service.mapper;

import com.databahn.planes.controlplane.entity.ChangeFlagRequest;
import com.databahn.planes.model.changeflag.ChangeFlag;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface ChangeFlagMapper {
  @Mapping(target = "type", source = "entityType")
  ChangeFlag changeFlagEntityToChangeFlag(ChangeFlagRequest changeFlagRequest);
}
