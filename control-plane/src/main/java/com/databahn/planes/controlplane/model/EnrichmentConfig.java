package com.databahn.planes.controlplane.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class EnrichmentConfig {
  String schema;

  Mapping match;

  List<Mapping> mappings;

  @Data
  public static class Mapping {
    @JsonProperty("source_field")
    private String sourceField;

    @JsonProperty("lookup_field")
    private String lookupField;
  }
}
