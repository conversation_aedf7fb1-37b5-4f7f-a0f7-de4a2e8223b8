package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.entity.DataPlane;
import com.databahn.planes.controlplane.entity.DataPlaneTenantMapping;
import com.databahn.planes.controlplane.entity.Tenant;
import com.databahn.planes.controlplane.repo.DataplaneRepository;
import jakarta.persistence.EntityNotFoundException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class DataplaneService {

  private final DataplaneRepository dataplaneRepository;

  public void health(UUID dataPlaneId) {
    Optional<DataPlane> dp = dataplaneRepository.findById(dataPlaneId);
    if (dp.isPresent()) {
      Instant now = Instant.now();
      DataPlane dataPlanToUpdate = dp.get();
      dataPlanToUpdate.setHeartbeatAt(now);
      dataplaneRepository.save(dataPlanToUpdate);
    } else {
      log.error("Data Plane not found for id {}", dataPlaneId);
    }
  }

  public List<com.databahn.planes.model.dataplane.Tenant> getTenants(UUID id) {
    DataPlane dataPlane =
        dataplaneRepository
            .findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Data Plane not found"));
    Set<UUID> tenantIds =
        dataPlane.getDataPlaneTenantMappings().stream()
            .map(DataPlaneTenantMapping::getTenant)
            .map(Tenant::getId)
            .collect(Collectors.toSet());
    return tenantIds.stream()
        .map(com.databahn.planes.model.dataplane.Tenant::new)
        .collect(Collectors.toList());
  }
}
