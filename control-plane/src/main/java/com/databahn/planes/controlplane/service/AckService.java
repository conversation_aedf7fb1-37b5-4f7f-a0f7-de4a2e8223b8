package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.repo.AckRepository;
import com.databahn.planes.controlplane.service.mapper.AckMapper;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.model.ack.AckTypes;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AckService {

  private final AckRepository ackRepository;
  private final AckMapper ackMapper;
  private final ReplayService replayService;

  public void saveAck(List<Ack> acks) {
    acks.forEach(
        ack -> {
          if (ack.getType() == AckTypes.CHANGE_FLAG) {
            ackRepository.save(ackMapper.ackModelToChangeFlagAck(ack));
          } else if (ack.getType() == AckTypes.REPLAY) {
            ackRepository.save(ackMapper.ackModelToChangeFlagAck(ack));
            try {
              replayService.updateStatus(ack.replayStatus.getStatus());
            } catch (Exception e) {
              throw new RuntimeException(e);
            }
          }
        });
  }
}
