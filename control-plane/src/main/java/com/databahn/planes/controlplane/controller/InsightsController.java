package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.InsightsService;
import com.databahn.planes.model.insights.StagingInsights;
import com.databahn.planes.response.Response;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/insights")
public class InsightsController {

  private InsightsService insightsService;

  @PostMapping()
  public Response<?> indexInsights(@RequestBody StagingInsights insights) {
    if (insights == null || insights.insights() == null) {
      return Response.Ok("Nothing to save");
    }
    insightsService.postInsightsToKafka(insights.insights());
    return Response.Message("Insights saved");
  }
}
