package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.ChangeFlagService;
import com.databahn.planes.model.changeflag.ChangeFlag;
import com.databahn.planes.response.Response;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/change_flags")
@Slf4j
public class ChangeFlagController {

  private ChangeFlagService changeFlagService;

  @GetMapping()
  public Response<List<ChangeFlag>> getChangeFlags(
      @RequestHeader("tenantId") String tenantId,
      @RequestHeader("dataPlaneId") UUID dataPlaneId,
      @RequestParam(name = "since", required = false) Long since,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "100") Integer size) {
    Sort s = Sort.by(Sort.Direction.ASC, "timestamp");
    Pageable pageable = PageRequest.of(page, size, s);
    List<ChangeFlag> changeFlags =
        changeFlagService.getChangeFlags(tenantId, dataPlaneId, since, pageable);
    log.info(
        "Sending {} change flags for checkpoint {}, tenantId {}, dataPlaneId {}",
        changeFlags.size(),
        since,
        tenantId,
        dataPlaneId);
    return Response.Ok(changeFlags);
  }
}
