package com.databahn.planes.controlplane.service;

import com.databahn.planes.constants.ChangeFlagAction;
import com.databahn.planes.constants.EntityType;
import com.databahn.planes.controlplane.constants.StatsConstants;
import com.databahn.planes.controlplane.entity.*;
import com.databahn.planes.controlplane.mappers.EnrichmentMapper;
import com.databahn.planes.controlplane.mappers.LookupCacheRequestMapper;
import com.databahn.planes.controlplane.model.LookupCacheRequestWithIndex;
import com.databahn.planes.controlplane.model.OpenSearchSearchResponse;
import com.databahn.planes.controlplane.repo.*;
import com.databahn.planes.model.constants.LookupOperations;
import com.databahn.planes.model.constants.LookupRequestType;
import com.databahn.planes.model.constants.LookupType;
import com.databahn.planes.model.constants.Status;
import com.databahn.planes.model.lookup.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class LookupService {

  private IndexingService indexingService;
  private LookupCacheRequestMapper requestMapper;
  private OpenSearchService openSearchService;
  private EnrichmentRepository enrichmentRepository;
  private LookupRepository lookupRepository;
  private EnrichmentMapper enrichmentMapper;
  private LookupIndexCacheRequestRepository requestRepository;
  private LookupCacheRequestDataPlaneMappingRepository requestDataPlaneMappingRepository;
  private ObjectMapper objectMapper;
  private ChangeFlagProducer changeFlagProducer;
  private InsightsRuleRepo insightsRuleRepo;

  public void saveLookupValues(List<LookupValue> lookupValues) {
    for (LookupValue lookupValue : lookupValues) {
      String index = String.format("lookup_%s", lookupValue.getLookupId());
      LookupValueEntity entity = new LookupValueEntity();
      entity.setTenantId(lookupValue.getTenantId());
      entity.setLookupType("ENRICHMENT");
      entity.setUpdatedAt(System.currentTimeMillis());
      entity.setLookupId(lookupValue.getLookupId());
      Map<String, Object> attributeValues = lookupValue.getAttributeNameToValue();
      entity.setData(attributeValues);
      if (attributeValues != null && attributeValues.size() > 0) {
        Object idObj = attributeValues.get(lookupValue.getKeyAttributeName());
        if (idObj == null || Objects.toString(idObj).trim().length() == 0) {
          log.error("No key attribute value found for lookup {}", lookupValue.getLookupId());
          continue;
        }
        String id = String.format("%s#%s", lookupValue.getTenantId(), idObj);
        indexingService.postToKafkaAsync(
            StatsConstants.TOPIC_INDEXING_OPENSEARCH, index, id, entity);
      } else {
        log.error("No attribute values found for lookup {}", lookupValue.getLookupId());
        continue;
      }
    }
  }

  public List<LookupCacheRequest> getPendingLookupCacheRequests(UUID tenantId, UUID dataPlaneId) {
    List<LookupCacheRequestWithIndex> finalRequests = new ArrayList<>();
    List<LookupCacheRequestWithIndex> requests =
        requestRepository.findAllPendingRequests(tenantId, dataPlaneId);
    for (LookupCacheRequestWithIndex request : requests) {
      if (request.getRequest().getOperation() == LookupOperations.CREATE) {
        if (request.getLookupType() == LookupType.VOLUME_CONTROLLER) {
          finalRequests.add(request);
        }
        if (request.getLookupType() == LookupType.ENRICHMENT) {
          UUID lookUpId = request.getRequest().getLookup().getId();
          List<Enrichment> dpEnrichments =
              enrichmentRepository.findByLookup_IdAndTenantIdAndDataPlane_Id(
                  lookUpId, tenantId, dataPlaneId);
          if (!dpEnrichments.isEmpty()) {
            finalRequests.add(request);
          }
        }
      } else {
        if (request.getLookupType() == LookupType.VOLUME_CONTROLLER) {
          if (!request
              .getRequest()
              .getLookup()
              .getCacheRequestId()
              .equals(request.getRequest().getId())) {
            finalRequests.add(request);
          }
        }
        if (request.getLookupType() == LookupType.ENRICHMENT) {
          Optional<Enrichment> enrichment =
              enrichmentRepository.findByCacheRequestId(request.getRequest().getId());
          if (enrichment.isEmpty()) {
            finalRequests.add(request);
          }
        }
      }
    }
    return finalRequests.stream().map(requestMapper::toRequest).toList();
  }

  public CacheOperationDetails getLookupDataToCache(
      UUID requestId, LookupOperations lookupOperations, int size, Object[] searchAfter) {
    LookupIndexCacheRequest cacheRequest = getRequest(requestId, lookupOperations);
    if (cacheRequest.getType() != LookupRequestType.CACHE) {
      log.error("Request type is not CACHE for id {}", requestId);
      throw new IllegalArgumentException("Request type is not CACHE for id " + requestId);
    }
    UUID operationId = cacheRequest.getOperationId();
    if (operationId == null) {
      log.error("No operation id found for request {}", requestId);
      throw new IllegalArgumentException("No operation id found for request " + requestId);
    }
    List<LookupIndexCacheRequest> indexCacheRequestList =
        requestRepository.findByOperationIdAndType(operationId, LookupRequestType.INDEX);
    if (indexCacheRequestList.isEmpty()) {
      log.error("No index request found for operationId {}", operationId);
      throw new IllegalArgumentException("No index request found for operationId " + operationId);
    }
    if (indexCacheRequestList.size() > 1) {
      log.error("Multiple index requests found for operationId {}", operationId);
      throw new IllegalArgumentException(
          "Multiple index requests found for operationId " + operationId);
    }

    Lookup lookup = cacheRequest.getLookup();
    LookupType lookupType = lookup.getType();

    if (lookupType == LookupType.VOLUME_CONTROLLER
        && cacheRequest.getOperation() == LookupOperations.DELETE) {
      CacheOperation cacheOperation = processVolumeControllerDelete(lookup, cacheRequest);
      return new CacheOperationDetails(cacheOperation, null);
    }

    LookupIndexCacheRequest indexRequest = indexCacheRequestList.get(0);
    String indexName = indexRequest.getIndexName();
    if (StringUtils.isEmpty(indexName)) {
      log.error("No index name found for operationId {}", operationId);
      throw new IllegalArgumentException("No index name found for operationId " + operationId);
    }

    OpenSearchSearchResponse openSearchData =
        openSearchService.search(
            indexName, "data:*", 0, size, "_id", OpenSearchService.SortOrder.ASC, searchAfter);
    List<Map<String, Object>> osData = openSearchData.data();
    CacheOperation operation =
        switch (lookupType) {
          case VOLUME_CONTROLLER -> processVolumeControllerCache(lookup, cacheRequest, osData);
          case ENRICHMENT -> processEnrichmentCache(lookup, cacheRequest, osData);
        };
    return new CacheOperationDetails(operation, openSearchData.searchAfter());
  }

  public void updateLookupCacheRequestStatus(
      UUID requestId,
      UUID tenantId,
      UUID dataPlaneId,
      LookupOperations lookupOperations,
      Status status,
      String error) {
    LookupIndexCacheRequest cacheRequest = getRequest(requestId, lookupOperations);

    Optional<LookupCacheRequestDataPlaneMapping> dpMapping =
        requestDataPlaneMappingRepository.findByRequestIdAndTenantIdAndDataPlaneIdAndOperation(
            requestId, tenantId, dataPlaneId, lookupOperations);
    if (dpMapping.isEmpty()) {
      LookupCacheRequestDataPlaneMapping mapping = new LookupCacheRequestDataPlaneMapping();
      mapping.setId(UUID.randomUUID());
      mapping.setRequestId(requestId);
      mapping.setTenantId(tenantId);
      mapping.setDataPlaneId(dataPlaneId);
      mapping.setStatus(status);
      mapping.setOperation(lookupOperations);
      mapping.setErrorMessage(error);
      requestDataPlaneMappingRepository.save(mapping);
    } else {
      LookupCacheRequestDataPlaneMapping mapping = dpMapping.get();
      mapping.setStatus(status);
      mapping.setErrorMessage(error);
      requestDataPlaneMappingRepository.save(mapping);
    }

    if (cacheRequest.getOperation() == LookupOperations.CREATE) {
      Lookup lookup = cacheRequest.getLookup();
      if (lookup.getType() == LookupType.VOLUME_CONTROLLER) {
        if (status.isRunning()) {
          lookup.setCacheRequestId(requestId);
          lookup.setStatus(Status.DEPLOYING);
          lookupRepository.save(lookup);
          sendChangeFlag(lookup, dataPlaneId);
        } else {
          lookup.setStatus(Status.FAILURE);
          lookupRepository.save(lookup);
        }
      } else {
        if (status.isRunning()) {
          Optional<Enrichment> enrichment =
              enrichmentRepository.findByCacheRequestId(cacheRequest.getId());
          if (enrichment.isPresent()) {
            Enrichment enr = enrichment.get();
            enr.setStatus(Status.DEPLOYING);
            enrichmentRepository.save(enr);
            sendChangeFlag(enr, dataPlaneId);
          }
        }
      }
    }
  }

  private LookupIndexCacheRequest getRequest(UUID requestId, LookupOperations lookupOperations) {
    LookupIndexCacheRequestKey key = getLookupIndexCacheRequestKey(requestId, lookupOperations);
    Optional<LookupIndexCacheRequest> request = requestRepository.findById(key);
    if (request.isEmpty()) {
      log.error("No request found for id {}", requestId);
      throw new IllegalArgumentException("No request found for id " + requestId);
    }
    return request.get();
  }

  private static LookupIndexCacheRequestKey getLookupIndexCacheRequestKey(
      UUID requestId, LookupOperations lookupOperations) {
    LookupIndexCacheRequestKey key = new LookupIndexCacheRequestKey();
    key.setId(requestId);
    key.setOperation(lookupOperations);
    return key;
  }

  private void sendChangeFlag(Enrichment enrichment, UUID dataPlaneId) {
    EnrichmentChangeFlag enrichmentChangeFlag =
        enrichmentMapper.enrichmentToEnrichmentChangeFlag(enrichment);
    changeFlagProducer.sendChangeFlag(
        enrichment.getTenantId().toString(),
        dataPlaneId.toString(),
        EntityType.ENRICHMENT,
        ChangeFlagAction.ADD,
        enrichment.getId().toString(),
        enrichmentChangeFlag);
  }

  private void sendChangeFlag(Lookup lookup, UUID dataPlaneId) {
    LookupChangeFlag lookupChangeFlag = new LookupChangeFlag();
    lookupChangeFlag.setId(lookup.getId().toString());
    lookupChangeFlag.setTenantId(lookup.getTenantId().toString());
    lookupChangeFlag.setRequestId(lookup.getCacheRequestId().toString());
    changeFlagProducer.sendChangeFlag(
        lookup.getTenantId().toString(),
        dataPlaneId.toString(),
        EntityType.LOOKUP,
        ChangeFlagAction.ADD,
        lookup.getId().toString(),
        lookupChangeFlag);
  }

  private CacheOperation processVolumeControllerCache(
      Lookup lookup, LookupIndexCacheRequest cacheRequest, List<Map<String, Object>> osData) {
    if (cacheRequest.getOperation() == LookupOperations.CREATE) {
      return processVolumeControllerCreate(lookup, osData, cacheRequest.getId());
    }
    log.error(
        "Unsupported operation {} for lookup type {}, id {}",
        cacheRequest.getOperation(),
        lookup.getType(),
        cacheRequest.getId());
    throw new IllegalArgumentException(
        "Unsupported operation "
            + cacheRequest.getOperation()
            + " for lookup type "
            + lookup.getType());
  }

  private CacheOperation processVolumeControllerDelete(
      Lookup lookup, LookupIndexCacheRequest cacheRequest) {
    String setKey = volumeControllerLookupCacheKey(lookup, cacheRequest.getId());
    return CacheOperation.setRemove(setKey);
  }

  private CacheOperation processVolumeControllerCreate(
      Lookup lookup, List<Map<String, Object>> osData, UUID cacheRequestId) {
    List<String> values = new ArrayList<>();
    for (Map<String, Object> lookUpRecord : osData) {
      Object data = lookUpRecord.get("data");
      if (data instanceof Map) {
        Map<String, Object> dataMap = (Map<String, Object>) data;
        Object val = dataMap.get("value");
        String value = Objects.toString(val).toLowerCase();
        values.add(value);
      }
    }
    String setKey = volumeControllerLookupCacheKey(lookup, cacheRequestId);
    return CacheOperation.setAdd(setKey, values);
  }

  private String volumeControllerLookupCacheKey(Lookup lookup, UUID cacheRequestId) {
    return String.format("%s#%s#%s", lookup.getTenantId(), lookup.getId(), cacheRequestId);
  }

  private CacheOperation processEnrichmentCache(
      Lookup lookup, LookupIndexCacheRequest indexRequest, List<Map<String, Object>> osData) {
    return switch (indexRequest.getOperation()) {
      case CREATE -> processEnrichmentCreate(lookup, osData, indexRequest.getId());
      case DELETE -> processEnrichmentDelete(lookup, osData, indexRequest.getId());
    };
  }

  private CacheOperation processEnrichmentDelete(
      Lookup lookup, List<Map<String, Object>> osData, UUID cacheRequestId) {
    String lookupField = enrichmentLookupField(cacheRequestId);
    List<String> cacheDataToDelete = new ArrayList<>();
    for (Map<String, Object> lookUpRecord : osData) {
      Object data = lookUpRecord.get("data");
      if (data instanceof Map) {
        Map<String, Object> dataMap = (Map<String, Object>) data;
        if (dataMap.containsKey(lookupField)) {
          String lookUpValue = Objects.toString(dataMap.get(lookupField));
          String cacheKey =
              enrichmentLookupCacheKey(lookup, lookupField, lookUpValue, cacheRequestId);
          cacheDataToDelete.add(cacheKey);
        }
      }
    }
    return CacheOperation.del(cacheDataToDelete);
  }

  private CacheOperation processEnrichmentCreate(
      Lookup lookup, List<Map<String, Object>> osData, UUID cacheRequestId) {
    String lookupField = enrichmentLookupField(cacheRequestId);
    return processEnrichmentCreate(lookup, osData, cacheRequestId, lookupField);
  }

  private CacheOperation processEnrichmentCreate(
      Lookup lookup, List<Map<String, Object>> osData, UUID cacheRequestId, String lookupField) {
    Map<String, String> cacheData = new HashMap<>();
    for (Map<String, Object> lookUpRecord : osData) {
      Object data = lookUpRecord.get("data");
      if (data instanceof Map) {
        Map<String, Object> dataMap = (Map<String, Object>) data;
        if (dataMap.containsKey(lookupField)) {
          String lookUpValue = Objects.toString(dataMap.get(lookupField));
          String cacheKey =
              enrichmentLookupCacheKey(lookup, lookupField, lookUpValue, cacheRequestId);
          String cacheValue = toJson(dataMap);
          cacheData.put(cacheKey, cacheValue);
        }
      }
    }
    return CacheOperation.put(cacheData);
  }

  private String enrichmentLookupField(UUID cacheRequestId) {
    Optional<Enrichment> enr = enrichmentRepository.findByCacheRequestId(cacheRequestId);
    if (enr.isEmpty()) {
      log.error("No enrichment found for cache request id {}", cacheRequestId);
      throw new IllegalArgumentException(
          "No enrichment found for cache request id " + cacheRequestId);
    }
    Enrichment enrichment = enr.get();
    return enrichment.getConfig().getMatch().getLookupField();
  }

  private String toJson(Map<String, Object> dataMap) {
    try {
      return objectMapper.writeValueAsString(dataMap);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  private String enrichmentLookupCacheKey(
      Lookup lookup, String keyField, String keyValue, UUID cacheRequestId) {
    return String.format(
        "%s#%s#%s#%s#%s",
        lookup.getTenantId(), lookup.getId(), cacheRequestId, keyField, keyValue.toLowerCase());
  }

  public List<DynamicLookupCacheRequest> getPendingDynamicLookupCacheRequests(
      UUID tenantId, UUID dataPlaneId) {
    List<InsightsRule> insightsWithLookup =
        insightsRuleRepo.findByDefineLookupTrueAndTenantIdAndDataPlane_Id(tenantId, dataPlaneId);
    List<DynamicLookupCacheRequest> requests = new ArrayList<>();
    for (InsightsRule rule : insightsWithLookup) {
      Optional<Lookup> lookupFound = lookupRepository.findById(rule.getLookupId());
      if (lookupFound.isEmpty()) {
        log.error("No lookup found for id {}", rule.getLookupId());
        continue;
      }
      Lookup lookup = lookupFound.get();

      switch (lookup.getType()) {
        case VOLUME_CONTROLLER -> {
          DynamicLookupCacheRequest request =
              baseDynamicLookupCacheRequest(tenantId, dataPlaneId, rule, lookup);
          requests.add(request);
        }
        case ENRICHMENT -> {
          List<Enrichment> enrichments =
              enrichmentRepository.findByLookup_IdAndTenantIdAndDataPlane_Id(
                  lookup.getId(), tenantId, dataPlaneId);
          for (Enrichment enrichment : enrichments) {
            if (enrichment.getStatus() == Status.DISABLED
                || enrichment.getStatus() == Status.DELETED) {
              continue;
            }
            DynamicLookupCacheRequest enrRequest =
                baseDynamicLookupCacheRequest(tenantId, dataPlaneId, rule, lookup);
            enrRequest.setId(enrichment.getCacheRequestId());
            enrRequest.setEnrichmentId(enrichment.getId());
            requests.add(enrRequest);
          }
        }
      }
    }
    return requests;
  }

  public DynamicCacheOperationDetails getDynamicLookupDataToCache(
      DynamicCacheOperationRequest request) {
    UUID lookupId = request.getLookupId();
    Lookup lookup =
        lookupRepository
            .findById(lookupId)
            .orElseThrow(() -> new IllegalArgumentException("No lookup found for id " + lookupId));
    if (!lookup.getIsDynamic()) {
      throw new IllegalArgumentException("Lookup with id " + lookupId + " is not dynamic");
    }
    LookupType lookupType = lookup.getType();
    String indexName = lookup.getIndexName();
    if (StringUtils.isEmpty(indexName)) {
      throw new IllegalArgumentException("No index name found for lookup with id " + lookupId);
    }
    int size = request.getSize();
    Object[] searchAfter = request.getSearchAfter();
    Long afterTimestamp = request.getAfterTimestamp();
    String query = "*";
    if (afterTimestamp != null) {
      query = String.format("created_at:>%d", afterTimestamp);
    }

    OpenSearchSearchResponse openSearchData =
        openSearchService.search(
            indexName, query, 0, size, "_id", OpenSearchService.SortOrder.ASC, searchAfter);
    List<Map<String, Object>> osData = openSearchData.data();

    CacheOperation operation =
        switch (lookupType) {
          case VOLUME_CONTROLLER -> processVolumeControllerCreate(
              lookup, osData, request.getRequestId());
          case ENRICHMENT -> {
            UUID enrichmentId = request.getEnrichmentId();
            Enrichment enrichment =
                enrichmentRepository
                    .findById(enrichmentId)
                    .orElseThrow(
                        () ->
                            new IllegalArgumentException(
                                "No enrichment found for id " + enrichmentId));
            String lookupField = enrichment.getConfig().getMatch().getLookupField();
            yield processEnrichmentCreate(lookup, osData, request.getRequestId(), lookupField);
          }
        };
    Long lastCreatedAt = null;
    for (Map<String, Object> datum : openSearchData.data()) {
      if (lastCreatedAt == null) {
        lastCreatedAt = (Long) datum.get("created_at");
      } else {
        lastCreatedAt = Math.max(lastCreatedAt, (Long) datum.get("created_at"));
      }
    }
    return new DynamicCacheOperationDetails(operation, openSearchData.searchAfter(), lastCreatedAt);
  }

  private static DynamicLookupCacheRequest baseDynamicLookupCacheRequest(
      UUID tenantId, UUID dataPlaneId, InsightsRule rule, Lookup lookup) {
    DynamicLookupCacheRequest request = new DynamicLookupCacheRequest();
    request.setId(lookup.getCacheRequestId());
    request.setLookupId(lookup.getId());
    request.setInsightRuleId(rule.getId());
    request.setLookupType(lookup.getType());
    request.setTenantId(tenantId);
    request.setDataPlaneId(dataPlaneId);
    return request;
  }
}
