package com.databahn.planes.controlplane.service.mapper;

import com.databahn.planes.controlplane.entity.ChangeFlagAck;
import com.databahn.planes.model.ack.Ack;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface AckMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "timestamp", expression = "java(java.time.Instant.now())")
  List<ChangeFlagAck> ackModelToChangeFlagAckList(List<Ack> ack);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "timestamp", expression = "java(java.time.Instant.now())")
  ChangeFlagAck ackModelToChangeFlagAck(Ack ack);
}
