package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.KafkaMetricsPublisher;
import com.databahn.planes.response.Response;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/metrics")
public class MetricController {

  private final KafkaMetricsPublisher kafkaMetricsPublisher;

  @RequestMapping(
      method = RequestMethod.POST,
      value = "/{type}",
      consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public Response<Object> saveMetrics(
      @PathVariable("type") String metricType, @RequestBody String stats) {

    String topicName = generateTopicName(metricType);
    log.debug("Processing {} metric ({} bytes)", metricType, stats.length());

    kafkaMetricsPublisher.publish(topicName, stats);
    return Response.Ok(Map.of("status", "success", "type", metricType, "size", stats.length()));
  }

  private String generateTopicName(String metricType) {
    // Customize based on your topic naming conventions
    return switch (metricType) {
      case "kafka" -> "db.metrics.kafka";
      case "kubernetes" -> "db.metrics.kubernetes";
      case "node" -> "db.metrics.node";
      case "redis" -> "db.metrics.redis";
      default -> "db.metrics." + metricType;
    };
  }
}
