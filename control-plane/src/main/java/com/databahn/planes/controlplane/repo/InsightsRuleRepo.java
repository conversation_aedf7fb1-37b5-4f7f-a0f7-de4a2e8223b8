package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.InsightsRule;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface InsightsRuleRepo extends JpaRepository<InsightsRule, UUID> {
  List<InsightsRule> findByDefineLookupTrueAndTenantIdAndDataPlane_Id(
      UUID tenantId, UUID dataPlaneId);
}
