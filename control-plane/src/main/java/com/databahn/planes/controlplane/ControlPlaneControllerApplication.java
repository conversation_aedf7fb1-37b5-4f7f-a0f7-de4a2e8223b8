package com.databahn.planes.controlplane;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(
    scanBasePackages = {"com.databahn.planes"},
    exclude = ElasticsearchDataAutoConfiguration.class)
@EnableFeignClients(basePackages = "com.databahn.planes.controlplane.client")
public class ControlPlaneControllerApplication {
  public static void main(String[] args) {
    SpringApplication.run(ControlPlaneControllerApplication.class, args);
  }
}
