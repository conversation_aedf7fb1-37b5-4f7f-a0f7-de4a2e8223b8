package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.AlertsService;
import com.databahn.planes.exception.PartialFailureException;
import com.databahn.planes.model.alerts.Alerts;
import com.databahn.planes.response.Response;
import com.google.common.collect.Maps;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/alerts")
public class AlertsController {

  private final AlertsService alertsService;

  @PostMapping()
  public Response<?> upsertAlerts(@RequestBody Alerts alerts) {
    if (alerts == null || alerts.alerts() == null) {
      return Response.Message("Nothing to save");
    }
    Map<Integer, Exception> results = alertsService.upsertAlerts(alerts.alerts());
    long errorCount = results.values().stream().filter(Objects::nonNull).count();
    if (errorCount > 0) {
      Map<String, Response<?>> response = Maps.newHashMap();
      results.forEach(
          (index, ex) -> {
            Response<String> r = ex == null ? Response.Message("Alert saved") : Response.Error(ex);
            response.put(String.valueOf(index), r);
          });
      throw new PartialFailureException(response);
    }
    return Response.Message("Alerts saved");
  }
}
