package com.databahn.planes.controlplane.entity;

import com.databahn.planes.model.constants.LookupOperations;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LookupIndexCacheRequestKey implements Serializable {
  private UUID id;
  private LookupOperations operation;
}
