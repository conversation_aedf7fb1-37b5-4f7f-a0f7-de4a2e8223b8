package com.databahn.planes.controlplane.entity;

import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataPlaneTenantMappingId implements Serializable {
  private UUID tenantId;
  private UUID dataPlaneId;
}
