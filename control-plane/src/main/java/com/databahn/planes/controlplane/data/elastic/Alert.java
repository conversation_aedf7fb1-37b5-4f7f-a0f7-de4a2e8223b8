package com.databahn.planes.controlplane.data.elastic;

import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

@Document(indexName = "db_alerts")
@NoArgsConstructor
@Data
public class Alert {
  @Id String id;
  private String title;
  private String criticality;
  private String message;
  private Date createdAt;
  private Date updatedAt;
  private Date firstObservedAt;
  private Date lastObservedAt;
  private String tenantId;
  private String functionalityType;
  private String functionality;
  private String functionalityEntityId;
  private String functionalityEntityName;
  private boolean dismissed;
  private int status;
  private String updatedBy;
  private String alertType;
  private String dataPlaneId;
  private String errorMessage;
  private String errorCode;
}
