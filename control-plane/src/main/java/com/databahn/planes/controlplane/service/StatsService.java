package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.constants.StatsConstants;
import com.databahn.planes.model.stats.Stat;
import com.databahn.planes.model.stats.VectorStats;
import com.fasterxml.jackson.databind.JsonNode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class StatsService {

  private IndexingService indexingService;
  private final String statsIndex;

  public StatsService(
      IndexingService indexingService, @Value("${open_search.stats_index}") String statsIndex) {
    this.indexingService = indexingService;
    this.statsIndex = statsIndex;
  }

  public void postStatsToKafka(List<Stat> stats) {
    stats.forEach(
        stat ->
            indexName(stat)
                .ifPresent(
                    indexName ->
                        this.indexingService.postToKafkaAsync(
                            StatsConstants.TOPIC_INDEXING_OPENSEARCH_STATISTICS,
                            indexName,
                            stat.id(),
                            stat)));
  }

  public void postOpenTelStatsToKafka(List<JsonNode> stats) {
    stats.forEach(
        stat ->
            this.indexingService.postToKafkaSync(
                StatsConstants.TOPIC_INDEXING_OPENSEARCH_OTEL, null, null, stat));
  }

  public void postVectorToKafka(VectorStats stats) {
    stats
        .stats()
        .forEach(
            stat ->
                this.indexingService.postToKafkaSync(
                    StatsConstants.TOPIC_INDEXING_OPENSEARCH_VECTOR, null, null, stat));
  }

  private Optional<String> indexName(Stat stat) {
    if (stat.tags() == null || stat.tags().isEmpty()) {
      log.error("Tags are missing in the stat: " + stat);
      return Optional.empty();
    }
    Object tenantId = stat.tags().get(StatsConstants.STATA_TAG_TENANT_ID);
    if (Objects.isNull(tenantId)) {
      log.error("Tenant id is missing in the stat: " + stat);
      return Optional.empty();
    }
    Object timeStampWindow = stat.tags().get(StatsConstants.STATA_TAG_TS_WIN);
    if (Objects.isNull(timeStampWindow)) {
      log.error("Timestamp window is missing in the stat: " + stat);
      return Optional.empty();
    }
    try {
      Long timeStamp = Long.parseLong(timeStampWindow.toString());
      return Optional.of(statsIndex + "_" + tenantId + "_v2_p1_" + getYearDay(timeStamp));
    } catch (NumberFormatException e) {
      log.error("Timestamp window is not a number in the stat: " + stat);
      return Optional.empty();
    }
  }

  private String getYearWeek(Long epoch) {
    Instant instant = Instant.ofEpochMilli(epoch);
    LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));

    int year = dateTime.get(WeekFields.of(Locale.getDefault()).weekBasedYear());
    int week = dateTime.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());
    return year + "_" + week;
  }

  static String getYearDay(Long epoch) {
    Instant instant = Instant.ofEpochMilli(epoch);
    LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
    int year = dateTime.getYear();
    int day = dateTime.getDayOfYear();
    return String.format("y%d_d%d", year, day);
  }
}
