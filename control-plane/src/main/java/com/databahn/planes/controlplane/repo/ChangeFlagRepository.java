package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.ChangeFlagRequest;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ChangeFlagRepository extends JpaRepository<ChangeFlagRequest, UUID> {
  List<ChangeFlagRequest>
      findByTenantIdAndDataPlaneIdAndSequenceIdGreaterThanAndBodyNotNullOrderBySequenceIdAsc(
          String tenantId, UUID dataPlaneId, Long sequenceId, Pageable pageable);

  List<ChangeFlagRequest> findByTenantIdAndDataPlaneIdAndBodyNotNullOrderBySequenceId(
      String tenantId, UUID dataPlaneId, Pageable pageable);
}
