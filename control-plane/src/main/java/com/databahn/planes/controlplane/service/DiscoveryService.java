package com.databahn.planes.controlplane.service;

import com.databahn.planes.controlplane.constants.StatsConstants;
import com.databahn.planes.model.discovery.DiscoverDevice;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DiscoveryService {
  private final IndexingService indexingService;

  public DiscoveryService(IndexingService indexingService) {
    this.indexingService = indexingService;
  }

  public void postDiscoveryToKafka(List<DiscoverDevice> discovery) {
    discovery.forEach(
        devices -> {
          String index =
              devices.tags().get("index") != null ? devices.tags().get("index").toString() : null;
          if (index != null) {
            log.info("Posting discovery to kafka: " + index);
            this.indexingService.postToKafkaSync(
                StatsConstants.TOPIC_INDEXING_OPENSEARCH, index, null, devices);
          } else {
            log.warn("Index is null, skipping posting discovery to Kafka.");
          }
        });
  }
}
