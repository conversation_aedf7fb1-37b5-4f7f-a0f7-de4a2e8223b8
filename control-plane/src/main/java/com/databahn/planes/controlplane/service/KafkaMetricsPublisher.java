package com.databahn.planes.controlplane.service;

import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class KafkaMetricsPublisher {

  private final KafkaTemplate<String, String> kafkaTemplate;

  public KafkaMetricsPublisher(
      @Qualifier("kafkaStringTemplate") KafkaTemplate<String, String> kafkaTemplate) {
    this.kafkaTemplate = kafkaTemplate;
  }

  @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 2000))
  public void publish(String topic, String data) {
    if (topic == null || topic.isBlank()) {
      log.error("Topic name cannot be null or empty.");
      throw new IllegalArgumentException("Topic name cannot be null or empty.");
    }

    if (data == null || data.isEmpty()) {
      log.error("Cannot publish empty metrics data to topic '{}'", topic);
      throw new IllegalArgumentException("Metrics data cannot be null or empty.");
    }

    CompletableFuture<?> future = kafkaTemplate.send(topic, data).toCompletableFuture();
    future.whenComplete(
        (result, ex) -> {
          if (ex != null) {
            log.error(
                "Failed to publish metrics to topic '{}'. Error: {}", topic, ex.getMessage(), ex);
          } else {
            log.info(
                "Successfully published metrics to topic '{}', data size: {} bytes",
                topic,
                data.length());
          }
        });
  }
}
