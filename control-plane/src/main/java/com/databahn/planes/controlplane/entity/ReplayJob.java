package com.databahn.planes.controlplane.entity;

import com.fasterxml.jackson.annotation.JsonRawValue;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.util.Date;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

@Data
@Entity
@Table(name = "replay_jobs")
@NoArgsConstructor
@AllArgsConstructor
public class ReplayJob {

  @Id
  @UuidGenerator
  @Column(name = "id")
  private UUID id;

  @Column(name = "name", unique = true, nullable = false)
  @NotBlank(message = "Name cannot be blank.")
  private String name;

  @Column(name = "description")
  private String description; // description type path

  @Column(name = "type", nullable = false)
  @NotBlank(message = "type cannot be blank.")
  private String type;

  @Column(name = "path")
  private String path;

  @Column(name = "config")
  private String config;

  @Column(name = "created_by")
  private UUID createdBy;

  @Column(name = "tenant_id")
  private UUID tenantId;

  @Column(name = "customer_id")
  private UUID customerId;

  @Column(name = "source_id")
  private UUID sourceId;

  @Column(name = "destination_id")
  private UUID destinationId;

  @Column(name = "start_time")
  private Date startTime;

  @Column(name = "end_time")
  private Date endTime;

  @Column(name = "status")
  private String status;

  @Column(name = "updated_by")
  private UUID updatedBy;

  @Column(name = "created_on")
  @CreationTimestamp
  private Date createdOn;

  @Column(name = "last_updated_on")
  @UpdateTimestamp
  private Date lastUpdated;

  @Column(name = "stats")
  @JsonRawValue
  private String stats;

  @Column(name = "progress")
  private Float progress;

  @Column(name = "device_type")
  private String deviceType;

  @Column(name = "device_vendor")
  private String deviceVendor;

  @Column(name = "log_type")
  private String logType;

  @Column(name = "fleet_id")
  private UUID fleetId;

  @Column(name = "connect_id")
  private UUID connectId;

  @Column(name = "replay_type")
  private String replayType;

  @Column(name = "parent_source_id")
  private UUID parentSourceId;

  @ManyToOne()
  @JoinColumn(name = "data_plane_id", referencedColumnName = "id")
  private DataPlane dataPlane;
}
