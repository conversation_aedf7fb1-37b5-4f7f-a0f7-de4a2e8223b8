package com.databahn.planes.controlplane.config;

import java.net.URL;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import lombok.SneakyThrows;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.data.client.orhlc.AbstractOpenSearchConfiguration;
import org.opensearch.data.client.orhlc.ClientConfiguration;
import org.opensearch.data.client.orhlc.RestClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenSearchConfiguration extends AbstractOpenSearchConfiguration {

  @Value("${open_search.username}")
  private String username;

  @Value("${open_search.password}")
  private String password;

  @Value("${open_search.url}")
  private String url;

  @Value("${open_search.enable_ssl:false}")
  private boolean enableSsl;

  @Override
  @Bean
  @SneakyThrows
  public RestHighLevelClient opensearchClient() {
    URL url = new URL(this.url);
    String host = url.getHost();
    int port = url.getPort() == -1 ? url.getDefaultPort() : url.getPort();
    ClientConfiguration.MaybeSecureClientConfigurationBuilder builder =
        ClientConfiguration.builder().connectedTo(host + ":" + port);
    ClientConfiguration.TerminalClientConfigurationBuilder configBuilder;
    if (enableSsl) {
      configBuilder = builder.usingSsl();
    } else {
      // for local testing
      SSLContext sslContext = SSLContext.getInstance("SSL");
      TrustManager[] skipCertificateValidation = new TrustManager[] {new NoopX509TrustManager()};
      sslContext.init(null, skipCertificateValidation, null);
      SkipHostnameVerifier allHostsValid = new SkipHostnameVerifier();
      configBuilder = builder.usingSsl(sslContext, allHostsValid);
    }
    ClientConfiguration configuration =
        configBuilder.withBasicAuth(this.username, this.password).build();
    return RestClients.create(configuration).rest();
  }

  private static class NoopX509TrustManager implements javax.net.ssl.X509TrustManager {
    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {}

    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {}

    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
      return new java.security.cert.X509Certificate[] {};
    }
  }

  private static class SkipHostnameVerifier implements javax.net.ssl.HostnameVerifier {
    @Override
    public boolean verify(String s, SSLSession sslSession) {
      return true;
    }
  }
}
