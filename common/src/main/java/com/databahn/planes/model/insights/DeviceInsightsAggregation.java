package com.databahn.planes.model.insights;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public record DeviceInsightsAggregation(
    @JsonProperty("INSIGHT_KEY") String insightsKey,
    @JsonProperty("MIN_TIME") Long minTime,
    @JsonProperty("MAX_TIME") Long maxTime,
    @JsonProperty("COUNT") Double count) {}
