package com.databahn.planes.model.constants;

public enum Status {
  CREATED(0),
  ACCEPTED(1),
  DEPLOYING(2),
  ACTIVE(3),
  DISABLED(4),
  DELETED(5),
  DISABLE_ERROR(6),
  ERRORED(7),
  SUPPRESSED(8),
  FAILURE(9),
  SUCCESS(10),

  PENDING(11);

  private int value;

  Status(int value) {
    this.value = value;
  }

  public int getValue() {
    return value;
  }

  public boolean isRunning() {
    return (value >= 1 && value <= 3) || value == 10;
  }
}
