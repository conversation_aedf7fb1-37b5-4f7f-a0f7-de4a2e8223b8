package com.databahn.planes.model.lookup;

import com.databahn.planes.model.constants.LookupOperations;
import com.databahn.planes.model.constants.LookupType;
import com.databahn.planes.model.constants.Status;
import java.util.Date;
import java.util.UUID;
import lombok.Data;

@Data
public class LookupCacheRequest {

  private UUID id;
  private UUID operationId;
  private UUID lookupId;
  private UUID tenantId;
  private LookupOperations operation;
  private Date createdAt;
  private Status requestStatus;
  private LookupType lookupType;
}
