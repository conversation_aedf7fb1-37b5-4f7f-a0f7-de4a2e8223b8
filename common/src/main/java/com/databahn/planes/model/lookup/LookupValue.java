package com.databahn.planes.model.lookup;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

@Data
public class LookupValue {
  @JsonProperty("key_attribute_name")
  private String keyAttributeName;

  @JsonProperty("attribute_name_to_value")
  private Map<String, Object> attributeNameToValue;

  @JsonProperty("lookup_id")
  private String lookupId;

  @JsonProperty("tenant_id")
  private String tenantId;

  @JsonProperty("vendor")
  private String vendor;

  @JsonProperty("device")
  private String device;
}
