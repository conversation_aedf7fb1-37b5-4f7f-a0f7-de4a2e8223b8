package com.databahn.planes.model.lookup;

import com.databahn.planes.model.constants.LookupType;
import java.util.Date;
import java.util.UUID;
import lombok.Data;

@Data
public class DynamicLookupCacheRequest {

  private UUID id;
  private UUID lookupId;
  private UUID insightRuleId;
  private LookupType lookupType;
  private UUID tenantId;
  private UUID dataPlaneId;
  private UUID enrichmentId;
  private Date createdAt;
}
