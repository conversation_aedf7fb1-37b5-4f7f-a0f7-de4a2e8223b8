package com.databahn.planes.model.lookup;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DynamicCacheOperationDetails extends CacheOperationDetails {
  private Long checkpointTimestamp;

  public DynamicCacheOperationDetails(
      CacheOperation operation, Object[] searchAfter, Long checkpointTimestamp) {
    super(operation, searchAfter);
    this.checkpointTimestamp = checkpointTimestamp;
  }
}
