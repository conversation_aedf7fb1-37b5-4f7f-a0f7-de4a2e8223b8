package com.databahn.planes.model.checkpoint;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Data;

@Data
public class CloudSourceCheckpoint extends CloudSourceCheckpointId {
  @JsonProperty("data_plane_id")
  protected UUID dataPlaneId;

  @JsonProperty("checkpoint")
  protected String checkpoint;

  @JsonProperty("timestamp")
  protected Long timestamp;
}
