package com.databahn.planes.model.ack;

import com.databahn.planes.model.replay.ReplayStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Ack {

  public String id;

  @JsonProperty("request_id")
  public String requestId;

  @JsonProperty("customer_id")
  public String customerId;

  @JsonProperty("type")
  public AckTypes type;

  @JsonProperty("replay_status")
  public ReplayStatus replayStatus;

  @JsonProperty("entity_id")
  public String entityId;

  @JsonProperty("entity_type")
  public String entityType;

  @JsonProperty("entity_version")
  public String entityVersion;

  @JsonProperty("service_name")
  private String serviceName;

  @JsonProperty("tenant_id")
  public String tenantId;

  public String action;

  public String status;

  public String error;

  public String progress;
}
