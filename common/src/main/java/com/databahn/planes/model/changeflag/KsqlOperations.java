package com.databahn.planes.model.changeflag;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class KsqlOperations {
  @JsonProperty("queries")
  private List<KsqlQuery> queries = new ArrayList<>();

  public void addQuery(KsqlQuery query) {
    queries.add(query);
  }

  public boolean hasQueries() {
    return queries.isEmpty();
  }

  @Data
  public static class KsqlQuery {
    @JsonProperty("query")
    private String query;
  }
}
