package com.databahn.planes.model.changeflag;

import java.util.List;
import lombok.Data;

@Data
public class AdminChangeFlag {
  private AdminAction action;
  private AdminSubAction subAction;
  private KsqlOperations ksqlOperations;

  public enum AdminAction {
    SETUP_KSQL_DB(
        AdminSubAction.KSQL_DB_AGG_RULE,
        AdminSubAction.KSQL_DB_SOURCE_CHECKPOINT,
        AdminSubAction.KSQL_DB_DEVICE_INSIGHTS),
    CREATE_KAFKA_TOPICS;

    private List<AdminSubAction> allowedSubActions;

    AdminAction(AdminSubAction... allowedSubActions) {
      this.allowedSubActions = List.of(allowedSubActions);
    }

    public List<AdminSubAction> getAllowedSubActions() {
      return allowedSubActions;
    }
  }

  public enum AdminSubAction {
    KSQL_DB_AGG_RULE,
    KSQL_DB_SOURCE_CHECKPOINT,
    KSQL_DB_DEVICE_INSIGHTS,
  }
}
