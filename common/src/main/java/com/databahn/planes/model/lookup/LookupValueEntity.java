package com.databahn.planes.model.lookup;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

@Data
public class LookupValueEntity {
  @JsonProperty("lookup_id")
  private String lookupId;

  @JsonProperty("tenant_id")
  private String tenantId;

  @JsonProperty("customer_id")
  private String customerId;

  @JsonProperty("lookup_type")
  private String lookupType;

  @JsonProperty("data")
  private Map<String, Object> data;

  @JsonProperty("updated_at")
  private Long updatedAt;
}
