package com.databahn.planes.model.insights;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public record StagingInsight(
    @JsonProperty("key1") String key1,
    @JsonProperty("key2") String key2,
    @JsonProperty("key3") String key3,
    @JsonProperty("key4") String key4,
    @JsonProperty("key5") String key5,
    @JsonProperty("type") String type,
    @JsonProperty("source_id") String sourceId,
    @JsonProperty("tenant_id") String tenantId,
    @JsonProperty("data_plane_id") String dataPlaneId,
    @JsonProperty("lookup_id") String lookupId,
    @JsonProperty("min_time") Long minTime,
    @JsonProperty("max_time") Long maxTime,
    @JsonProperty("count") Double count) {}
