package com.databahn.planes.model.lookup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CacheOperation {

  private Operation operation;
  private Map<String, String> putValues;
  private List<String> keysToDel;
  private String setKey;
  private List<String> setValues;

  public static CacheOperation put(Map<String, String> putValues) {
    CacheOperation cacheOperation = new CacheOperation();
    cacheOperation.setOperation(Operation.PUT);
    cacheOperation.setPutValues(putValues);
    return cacheOperation;
  }

  public static CacheOperation del(List<String> keysToDel) {
    CacheOperation cacheOperation = new CacheOperation();
    cacheOperation.setOperation(Operation.DEL);
    cacheOperation.setKeysToDel(keysToDel);
    return cacheOperation;
  }

  public static CacheOperation setAdd(String setKey, List<String> setValues) {
    CacheOperation cacheOperation = new CacheOperation();
    cacheOperation.setOperation(Operation.SET_ADD);
    cacheOperation.setSetKey(setKey);
    cacheOperation.setSetValues(setValues);
    return cacheOperation;
  }

  public static CacheOperation setRemove(String setKey) {
    CacheOperation cacheOperation = new CacheOperation();
    cacheOperation.setOperation(Operation.SET_REMOVE);
    cacheOperation.setSetKey(setKey);
    return cacheOperation;
  }

  public boolean isEmpty() {
    return switch (operation) {
      case PUT -> putValues == null || putValues.isEmpty();
      case DEL -> keysToDel == null || keysToDel.isEmpty();
      case SET_ADD -> setValues == null || setValues.isEmpty();
      case SET_REMOVE -> setKey == null || setKey.isEmpty();
    };
  }

  public int count() {
    return switch (operation) {
      case PUT -> putValues.size();
      case DEL -> keysToDel.size();
      case SET_ADD -> setValues.size();
      case SET_REMOVE -> 1;
    };
  }

  public enum Operation {
    PUT, // put putValues as key value
    DEL, // delete keyToDel
    SET_ADD, // add setValues to setKey
    SET_REMOVE // remove set by setKey
  }
}
