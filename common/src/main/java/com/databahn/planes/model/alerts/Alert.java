package com.databahn.planes.model.alerts;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HexFormat;

public record Alert(
    String title,
    String criticality,
    String message,
    Date createdAt,
    Date updatedAt,
    Date firstObservedAt,
    Date lastObservedAt,
    String tenantId,
    String functionalityType,
    String functionality,
    String functionalityEntityId,
    String functionalityEntityName,
    boolean dismissed,
    int status,
    String updatedBy,
    String alertType,
    String dataPlaneId,
    String errorMessage,
    String errorCode) {

  public String buildId() {
    try {
      var alertIdBuilder =
          String.format(
              "tenantId=%s&entityId=%s&entityName=%s&functionality=%s&type=%s",
              this.tenantId,
              this.functionalityEntityId,
              this.functionalityEntityName,
              this.functionality,
              this.functionalityType);
      MessageDigest digest = MessageDigest.getInstance("SHA-256");
      byte[] hash = digest.digest(alertIdBuilder.getBytes(StandardCharsets.UTF_8));
      return HexFormat.of().formatHex(hash);
    } catch (NoSuchAlgorithmException e) {
      throw new RuntimeException(e);
    }
  }
}
