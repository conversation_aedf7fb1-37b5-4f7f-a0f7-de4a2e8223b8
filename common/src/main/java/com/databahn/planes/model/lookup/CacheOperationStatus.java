package com.databahn.planes.model.lookup;

import com.databahn.planes.model.constants.LookupOperations;
import com.databahn.planes.model.constants.Status;
import java.util.UUID;
import lombok.Data;

@Data
public class CacheOperationStatus {
  private UUID requestId;
  private UUID tenantId;
  private UUID dataPlaneId;
  private LookupOperations lookupOperations;
  private Status status;
  private String error;

  public static CacheOperationStatus active(
      UUID requestId, UUID tenantId, UUID dataPlaneId, LookupOperations lookupOperations) {
    CacheOperationStatus status = new CacheOperationStatus();
    status.setRequestId(requestId);
    status.setLookupOperations(lookupOperations);
    status.setTenantId(tenantId);
    status.setDataPlaneId(dataPlaneId);
    status.setStatus(Status.ACTIVE);
    return status;
  }

  public static CacheOperationStatus failure(
      UUID requestId,
      UUID tenantId,
      UUID dataPlaneId,
      LookupOperations lookupOperations,
      String error) {
    CacheOperationStatus status = new CacheOperationStatus();
    status.setRequestId(requestId);
    status.setLookupOperations(lookupOperations);
    status.setTenantId(tenantId);
    status.setDataPlaneId(dataPlaneId);
    status.setStatus(Status.FAILURE);
    status.setError(error == null ? null : error.substring(0, Math.min(1800, error.length())));
    return status;
  }
}
