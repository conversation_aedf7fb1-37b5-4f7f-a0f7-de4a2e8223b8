package com.databahn.planes.model.replay;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Status {

  private String fileName;
  private String requestId;
  private String status;
  private long fileSize;
  private long currentSize;
  private String filePath;
  private List<String> errorMsg;
  private Date startTime;
  private Date endTime;
  private float percentage;
  private int lines;
}
