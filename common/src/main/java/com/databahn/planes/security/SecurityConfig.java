package com.databahn.planes.security;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.oauth2.core.oidc.OidcIdToken;
import org.springframework.security.oauth2.core.oidc.user.OidcUserAuthority;
import org.springframework.security.oauth2.core.user.OAuth2UserAuthority;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRequestAttributeHandler;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

  private final String jwkSetUri;
  private final SimpleGrantedAuthoritiesMapper mapper = new SimpleGrantedAuthoritiesMapper();

  public SecurityConfig(
      @Value("${spring.security.oauth2.resourceserver.jwt.jwk-set-uri}") String jwkSetUri) {
    this.jwkSetUri = jwkSetUri;
  }

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    // @formatter:off
    http.csrf(
            csrf ->
                csrf.csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
                    .ignoringRequestMatchers("/internal/v1/**")
                    .csrfTokenRequestHandler(new CsrfTokenRequestAttributeHandler()))
        .authorizeHttpRequests(
            authorize ->
                authorize
                    .requestMatchers(HttpMethod.OPTIONS, "/v1/log-preview/**")
                    .permitAll()
                    .requestMatchers(HttpMethod.POST, "/internal/v1/**")
                    .permitAll()
                    .requestMatchers(HttpMethod.GET, "/internal/v1/**")
                    .permitAll()
                    .requestMatchers(
                        "/actuator/health",
                        "/actuator/prometheus",
                        "/oauth2/**",
                        "/login/oauth2/code/*")
                    .permitAll()
                    .anyRequest()
                    .fullyAuthenticated())
        .oauth2ResourceServer(
            oauth2 ->
                oauth2.jwt(
                    jwt ->
                        jwt.jwkSetUri(this.jwkSetUri)
                            .jwtAuthenticationConverter(keycloakJwtAuthenticationConverter())))
        .sessionManagement(smc -> smc.sessionCreationPolicy(SessionCreationPolicy.ALWAYS))
        .oauth2Login(
            oauth2 ->
                oauth2.userInfoEndpoint(
                    userInfo -> userInfo.userAuthoritiesMapper(keycloakUserAuthoritiesMapper())))
        .logout(logout -> logout.logoutSuccessHandler(logoutSuccessHandler()));
    // @formatter:on
    return http.build();
  }

  private SimpleUrlLogoutSuccessHandler logoutSuccessHandler() {
    SimpleUrlLogoutSuccessHandler logoutSuccessHandler = new SimpleUrlLogoutSuccessHandler();
    logoutSuccessHandler.setUseReferer(true);
    return logoutSuccessHandler;
  }

  @Bean
  public JwtAuthenticationConverter keycloakJwtAuthenticationConverter() {
    JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
    converter.setJwtGrantedAuthoritiesConverter(
        new KeycloakJwtGrantedAuthoritiesConverter(new SimpleGrantedAuthoritiesMapper()));
    return converter;
  }

  private GrantedAuthoritiesMapper keycloakUserAuthoritiesMapper() {
    return authorities -> {
      Set<GrantedAuthority> mappedAuthorities = new HashSet<>();
      authorities.forEach(
          authority -> {
            if (authority instanceof OidcUserAuthority oidcUserAuthority) {
              OidcIdToken idToken = oidcUserAuthority.getIdToken();
              mappedAuthorities.addAll(mapper.map(idToken.getClaims()));
            } else if (authority instanceof OAuth2UserAuthority oauth2UserAuthority) {
              Map<String, Object> userAttributes = oauth2UserAuthority.getAttributes();
              mappedAuthorities.addAll(mapper.map(userAttributes));
            }
          });
      return mappedAuthorities;
    };
  }
}
