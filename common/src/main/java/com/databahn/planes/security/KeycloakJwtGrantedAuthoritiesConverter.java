package com.databahn.planes.security;

import java.util.Collection;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;

@RequiredArgsConstructor
public class KeycloakJwtGrantedAuthoritiesConverter
    implements Converter<Jwt, Collection<GrantedAuthority>> {

  private final SimpleGrantedAuthoritiesMapper mapper;

  @Override
  public Collection<GrantedAuthority> convert(Jwt token) {
    return mapper.map(token.getClaims());
  }
}
