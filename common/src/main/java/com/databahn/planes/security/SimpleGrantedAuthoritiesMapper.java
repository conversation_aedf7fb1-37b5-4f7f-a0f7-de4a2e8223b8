package com.databahn.planes.security;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.util.CollectionUtils;

public class SimpleGrantedAuthoritiesMapper {

  public static final String REALM_ACCESS = "realm_access";
  public static final String ROLES = "roles";

  @SuppressWarnings("unchecked")
  public Collection<GrantedAuthority> map(Map<String, Object> claims) {

    if (CollectionUtils.isEmpty(claims)) {
      return Collections.emptySet();
    }

    Map<String, Object> realmAccess = (Map<String, Object>) claims.get(REALM_ACCESS);
    if (!CollectionUtils.isEmpty(realmAccess)) {
      return ((Collection<String>) realmAccess.get(ROLES))
          .stream()
              .map(role -> "ROLE_" + role)
              .map(SimpleGrantedAuthority::new)
              .collect(Collectors.toUnmodifiableSet());
    }
    return Collections.emptySet();
  }
}
