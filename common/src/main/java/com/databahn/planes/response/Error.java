package com.databahn.planes.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Error {
  @JsonProperty("timestamp")
  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant timestamp;

  @JsonProperty("path")
  private String path;

  @JsonProperty("error")
  protected String error;

  @JsonProperty("message")
  protected String message;

  @JsonProperty("code")
  protected String code;
}
