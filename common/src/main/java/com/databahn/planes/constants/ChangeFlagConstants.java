package com.databahn.planes.constants;

public class ChangeFlagConstants {
  public static final String HeaderTenantId = "tenant_id";
  public static final String HeaderDataPlaneId = "data_plane_id";
  public static final String HeaderRequestId = "request_id";
  public static final String HeaderAction = "action";
  public static final String HeaderEntityType = "entity_type";
  public static final String HeaderSchemaVersion = "schema_version";
  public static final String HeaderEntityId = "entity_id";
  public static final String HeaderEntity = "entity";
  public static final String KafkaTopic = "db.management.change.flag";
  public static final String ADD = "add";
  public static final String UPDATE = "update";
  public static final String DELETE = "delete";
  public static final String ID = "id";
}
