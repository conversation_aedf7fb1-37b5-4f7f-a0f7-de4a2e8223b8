package com.databahn.planes.constants;

public enum EntityType implements ChangeFlagType {
  VC_RULE("rule"),
  SOURCE("source"),
  LOOKUP("lookup"),
  DESTINATION("destination"),
  TRANSFORMATION("transformer"),
  PIPELINE("pipeline"),
  ENRICHMENT("enrichment"),
  ROUTE_PROCESSOR("route_processor"),
  DATA_REPLAY("data_replay"),
  INSIGHTS_RULE("insights_rule"),
  ADMIN("admin");

  private String entityType;

  private EntityType(String entityType) {
    this.entityType = entityType;
  }

  public String getEntityType() {
    return entityType;
  }

  @Override
  public String getChangeFlagType() {
    if (this == DESTINATION) {
      throw new IllegalArgumentException(
          "DESTINATION is not a valid entity type for ChangeFlagType");
    }
    return entityType;
  }
}
