package com.databahn.planes.exception;

/**
 * Exception thrown when no tenants are found for a data plane when processing metrics. This is used
 * in multi-tenant environments to ensure metrics are properly associated with tenants.
 */
public class NoTenantsFoundException extends RuntimeException {
  public NoTenantsFoundException(String message) {
    super(message);
  }

  public NoTenantsFoundException(String message, Throwable cause) {
    super(message, cause);
  }
}
