package com.databahn.planes.exception;

import com.databahn.planes.response.Error;
import com.databahn.planes.response.MultiStatusResponse;
import com.databahn.planes.response.Response;
import jakarta.persistence.EntityNotFoundException;
import java.time.Instant;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@Slf4j
@RestControllerAdvice
public class CommonExceptionHandler extends ResponseEntityExceptionHandler {

  @ExceptionHandler({PartialFailureException.class})
  public ResponseEntity<MultiStatusResponse<?>> handlePartialFailureExceptions(
      PartialFailureException ex, WebRequest request) {
    Map<String, Response<?>> responses = ex.getResponses();
    log.error(
        "failed with partial failure for " + getPath(request) + ", failures + " + responses, ex);
    MultiStatusResponse<?> response = Response.Multi(responses);
    return new ResponseEntity(response, HttpStatus.MULTI_STATUS);
  }

  @ExceptionHandler({ValidationException.class})
  public ResponseEntity<?> handleValidationFailureExceptions(
      ValidationException ex, WebRequest request) {
    log.error("failed validation" + getPath(request), ex);
    return handleResponse(HttpStatus.BAD_REQUEST, ex.getMessage(), request);
  }

  @ExceptionHandler({RuntimeException.class})
  public ResponseEntity<Response<?>> handleValidationExceptions(
      RuntimeException ex, WebRequest request) {
    log.error("failed with runtime exception " + getPath(request), ex);
    return handleResponse(HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage(), request);
  }

  @ExceptionHandler({EntityNotFoundException.class})
  public ResponseEntity<Response<?>> handleNotFoundExceptions(
      EntityNotFoundException ex, WebRequest request) {
    return handleResponse(HttpStatus.NOT_FOUND, ex.getMessage(), request);
  }

  @ExceptionHandler({NoTenantsFoundException.class})
  public ResponseEntity<Response<?>> handleNoTenantsFoundExceptions(
      NoTenantsFoundException ex, WebRequest request) {
    log.error("No tenants found for data plane " + getPath(request), ex);
    return handleResponse(HttpStatus.BAD_REQUEST, ex.getMessage(), request);
  }

  private ResponseEntity<Response<?>> handleResponse(
      HttpStatus status, String message, WebRequest request) {
    Error error =
        Error.builder()
            .timestamp(Instant.now())
            .message(message)
            .error(status.getReasonPhrase())
            .path(getPath(request))
            .build();
    var response = Response.Error(error);
    return new ResponseEntity(response, HttpStatusCode.valueOf(status.value()));
  }

  private static String getPath(WebRequest request) {
    return ((ServletWebRequest) request).getRequest().getRequestURI();
  }
}
