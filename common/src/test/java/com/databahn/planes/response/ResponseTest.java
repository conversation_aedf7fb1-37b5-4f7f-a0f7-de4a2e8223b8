package com.databahn.planes.response;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

class ResponseTest {

  @SneakyThrows
  @Test
  void testSimpleSuccessJsonMapping() {
    ObjectMapper objectMapper = new ObjectMapper();
    Response<Object> response = Response.Ok("message3");

    String json = objectMapper.writeValueAsString(response);
    Response<Object> output = objectMapper.readValue(json, Response.class);

    assertThat(output.getData()).isEqualTo("message3");
    assertThat(output.getStatus()).isEqualTo(Response.Status.SUCCESS);
  }

  @SneakyThrows
  @Test
  void testSimpleMessageMapping() {
    ObjectMapper objectMapper = new ObjectMapper();
    Response<Object> response = Response.Message("message3");

    String json = objectMapper.writeValueAsString(response);
    Response<Object> output = objectMapper.readValue(json, Response.class);

    assertThat(output.getMessage()).isEqualTo("message3");
    assertThat(output.getData()).isEqualTo(null);
    assertThat(output.getStatus()).isEqualTo(Response.Status.SUCCESS);
  }

  @SneakyThrows
  @Test
  void testSimpleErrorMapping() {
    ObjectMapper objectMapper = new ObjectMapper();
    Error error = Error.builder().error("error").message("message").build();
    Response<Object> response = Response.Error(error);

    String json = objectMapper.writeValueAsString(response);
    Response<Object> output = objectMapper.readValue(json, Response.class);

    assertThat(output.getError()).isEqualTo(error);
    assertThat(output.getStatus()).isEqualTo(Response.Status.ERROR);
  }

  @SneakyThrows
  @Test
  void testJsonMappingMultiResponse() {
    ObjectMapper objectMapper = new ObjectMapper();
    Error error1 = Error.builder().error("error1").message("message1").code("code1").build();
    Response<Object> r1 = Response.Error(error1);
    Response<Object> r2 = Response.Message("message2");
    Response<Object> r3 = Response.Ok("message3");

    Map<String, Response<?>> responses = Map.of("1", r1, "2", r2, "3", r3);

    MultiStatusResponse<?> response = Response.Multi(responses);

    String json = objectMapper.writeValueAsString(response);

    System.out.println(json);

    Response<Object> output = objectMapper.readValue(json, Response.class);

    Object outputData = output.getData();
    Map<String, Response<?>> outputDataMap = (Map<String, Response<?>>) outputData;
    assertThat(outputDataMap).hasSize(3);
  }
}
