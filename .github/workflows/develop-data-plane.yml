name: Build Data Plane Controller CI

on:
  push:
    branches:
      - develop
    paths-ignore:
      - 'control-plane/**'

jobs:
  data-plane-controller:
    uses: databahn-ai/ci-github-workflows/.github/workflows/build-java-service.yaml@master
    with:
      service_name: data-plane-controller
      service_version: "v${{ github.sha }}"
      build_dir: data-plane
      platforms: linux/arm64/v8,linux/amd64
    secrets: inherit

  update-gitops-repo:
    needs:
      - data-plane-controller
    uses: databahn-ai/ci-github-workflows/.github/workflows/update-gitops-repo.yaml@master
    with:
      service_name: data-plane-controller
      service_version: "v${{ github.sha }}"
    secrets: inherit