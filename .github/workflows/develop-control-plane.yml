name: Build Control Plane Controller CI

on:
  push:
    branches:
      - develop
    paths-ignore:
      - 'data-plane/**'

jobs:
  control-plane-controller:
    uses: databahn-ai/ci-github-workflows/.github/workflows/build-java-service.yaml@master
    with:
      service_name: control-plane-controller
      service_version: "v${{ github.sha }}"
      build_dir: control-plane
      platforms: linux/arm64/v8,linux/amd64
    secrets: inherit

  update-gitops-repo:
    needs:
      - control-plane-controller
    uses: databahn-ai/ci-github-workflows/.github/workflows/update-gitops-repo.yaml@master
    with:
      service_name: control-plane-controller
      service_version: "v${{ github.sha }}"
    secrets: inherit