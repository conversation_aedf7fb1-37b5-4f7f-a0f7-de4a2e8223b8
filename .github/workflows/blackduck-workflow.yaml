name: BlackD<PERSON> Scan

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build:
    runs-on: blacksmith-4vcpu-ubuntu-2204

    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: useblacksmith/setup-java@v5
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven

      - name: Build with Ma<PERSON>
        run: ./mvnw -B package --file pom.xml

      - name: Black Duck Scan
        env:
          BLACKDUCK_URL: ${{ vars.BLACKDUCK_URL }}
          BLACKDUCK_API_TOKEN: ${{ secrets.BLACKDUCK_API_TOKEN }}
          DETECT_PROJECT_NAME: ${{ github.event.repository.name }}
          DETECT_PROJECT_VERSION_NAME: ${{ github.ref_name }}
          DETECT_CODE_LOCATION_NAME: ${{ github.event.repository.name }}-${{ github.ref_name }}
        run: |
          curl -fLsS -o ${{ runner.temp }}/detect10.sh https://detect.blackduck.com/detect10.sh && chmod +x ${{ runner.temp }}/detect10.sh
          ${{ runner.temp }}/detect10.sh
